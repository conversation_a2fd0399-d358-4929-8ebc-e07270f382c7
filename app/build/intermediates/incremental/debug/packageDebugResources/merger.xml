<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/paker1/app/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/paker1/app/src/main/res"><file name="email" path="/Users/<USER>/paker1/app/src/main/res/mipmap-mdpi/email.png" qualifiers="mdpi-v4" type="mipmap"/><file name="social_call" path="/Users/<USER>/paker1/app/src/main/res/mipmap-mdpi/social_call.png" qualifiers="mdpi-v4" type="mipmap"/><file name="arrow_right" path="/Users/<USER>/paker1/app/src/main/res/mipmap-mdpi/arrow_right.png" qualifiers="mdpi-v4" type="mipmap"/><file name="settings" path="/Users/<USER>/paker1/app/src/main/res/mipmap-mdpi/settings.png" qualifiers="mdpi-v4" type="mipmap"/><file name="social_facebook" path="/Users/<USER>/paker1/app/src/main/res/mipmap-mdpi/social_facebook.png" qualifiers="mdpi-v4" type="mipmap"/><file name="social_kakaotalk" path="/Users/<USER>/paker1/app/src/main/res/mipmap-mdpi/social_kakaotalk.png" qualifiers="mdpi-v4" type="mipmap"/><file name="nav_cnt_back" path="/Users/<USER>/paker1/app/src/main/res/mipmap-mdpi/nav_cnt_back.png" qualifiers="mdpi-v4" type="mipmap"/><file name="social_google" path="/Users/<USER>/paker1/app/src/main/res/mipmap-mdpi/social_google.png" qualifiers="mdpi-v4" type="mipmap"/><file name="social_sms" path="/Users/<USER>/paker1/app/src/main/res/mipmap-mdpi/social_sms.png" qualifiers="mdpi-v4" type="mipmap"/><file name="social_line" path="/Users/<USER>/paker1/app/src/main/res/mipmap-mdpi/social_line.png" qualifiers="mdpi-v4" type="mipmap"/><file name="android_question" path="/Users/<USER>/paker1/app/src/main/res/mipmap-mdpi/android_question.png" qualifiers="mdpi-v4" type="mipmap"/><file name="logo" path="/Users/<USER>/paker1/app/src/main/res/mipmap-mdpi/logo.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/paker1/app/src/main/res/mipmap-mdpi/ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/paker1/app/src/main/res/mipmap-mdpi/ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="call" path="/Users/<USER>/paker1/app/src/main/res/mipmap-mdpi/call.png" qualifiers="mdpi-v4" type="mipmap"/><file name="line" path="/Users/<USER>/paker1/app/src/main/res/mipmap-mdpi/line.png" qualifiers="mdpi-v4" type="mipmap"/><file name="android_call" path="/Users/<USER>/paker1/app/src/main/res/mipmap-mdpi/android_call.png" qualifiers="mdpi-v4" type="mipmap"/><file name="bounce" path="/Users/<USER>/paker1/app/src/main/res/anim/bounce.xml" qualifiers="" type="anim"/><file name="alpha" path="/Users/<USER>/paker1/app/src/main/res/anim/alpha.xml" qualifiers="" type="anim"/><file name="slidein" path="/Users/<USER>/paker1/app/src/main/res/anim/slidein.xml" qualifiers="" type="anim"/><file name="translate" path="/Users/<USER>/paker1/app/src/main/res/anim/translate.xml" qualifiers="" type="anim"/><file name="slideout" path="/Users/<USER>/paker1/app/src/main/res/anim/slideout.xml" qualifiers="" type="anim"/><file name="icon_logout" path="/Users/<USER>/paker1/app/src/main/res/drawable-v23/icon_logout.PNG" qualifiers="v23" type="drawable"/><file name="pop_accept" path="/Users/<USER>/paker1/app/src/main/res/drawable-v23/pop_accept.PNG" qualifiers="v23" type="drawable"/><file name="icon_close" path="/Users/<USER>/paker1/app/src/main/res/drawable-v23/icon_close.png" qualifiers="v23" type="drawable"/><file name="icon_search" path="/Users/<USER>/paker1/app/src/main/res/drawable-v23/icon_search.PNG" qualifiers="v23" type="drawable"/><file name="icon_search_mark" path="/Users/<USER>/paker1/app/src/main/res/drawable-v23/icon_search_mark.png" qualifiers="v23" type="drawable"/><file name="pop_reject" path="/Users/<USER>/paker1/app/src/main/res/drawable-v23/pop_reject.png" qualifiers="v23" type="drawable"/><file name="icon_block" path="/Users/<USER>/paker1/app/src/main/res/drawable-v23/icon_block.PNG" qualifiers="v23" type="drawable"/><file name="pop_close" path="/Users/<USER>/paker1/app/src/main/res/drawable-v23/pop_close.PNG" qualifiers="v23" type="drawable"/><file name="login_edit" path="/Users/<USER>/paker1/app/src/main/res/drawable-v23/login_edit.xml" qualifiers="v23" type="drawable"/><file name="round_button" path="/Users/<USER>/paker1/app/src/main/res/drawable-v23/round_button.xml" qualifiers="v23" type="drawable"/><file name="login_mark_email" path="/Users/<USER>/paker1/app/src/main/res/drawable-v23/login_mark_email.png" qualifiers="v23" type="drawable"/><file name="pop_close_gray" path="/Users/<USER>/paker1/app/src/main/res/drawable-v23/pop_close_gray.PNG" qualifiers="v23" type="drawable"/><file name="pop_result" path="/Users/<USER>/paker1/app/src/main/res/drawable-v23/pop_result.PNG" qualifiers="v23" type="drawable"/><file name="pop_mark" path="/Users/<USER>/paker1/app/src/main/res/drawable-v23/pop_mark.PNG" qualifiers="v23" type="drawable"/><file name="icon_mail" path="/Users/<USER>/paker1/app/src/main/res/drawable-v23/icon_mail.PNG" qualifiers="v23" type="drawable"/><file name="pop_back" path="/Users/<USER>/paker1/app/src/main/res/drawable-v23/pop_back.png" qualifiers="v23" type="drawable"/><file name="login_mark_pwd" path="/Users/<USER>/paker1/app/src/main/res/drawable-v23/login_mark_pwd.PNG" qualifiers="v23" type="drawable"/><file name="logo" path="/Users/<USER>/paker1/app/src/main/res/mipmap-hdpi/logo.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/paker1/app/src/main/res/mipmap-hdpi/ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/paker1/app/src/main/res/mipmap-hdpi/ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="img_alpha1" path="/Users/<USER>/paker1/app/src/main/res/drawable/img_alpha1.png" qualifiers="" type="drawable"/><file name="navigation_empty_icon" path="/Users/<USER>/paker1/app/src/main/res/drawable/navigation_empty_icon.xml" qualifiers="" type="drawable"/><file name="ic_mtrl_chip_checked_circle" path="/Users/<USER>/paker1/app/src/main/res/drawable/ic_mtrl_chip_checked_circle.xml" qualifiers="" type="drawable"/><file name="img_alpha0" path="/Users/<USER>/paker1/app/src/main/res/drawable/img_alpha0.png" qualifiers="" type="drawable"/><file name="img_alpha50" path="/Users/<USER>/paker1/app/src/main/res/drawable/img_alpha50.xml" qualifiers="" type="drawable"/><file name="main_title" path="/Users/<USER>/paker1/app/src/main/res/drawable/main_title.xml" qualifiers="" type="drawable"/><file name="recall_button_style" path="/Users/<USER>/paker1/app/src/main/res/drawable/recall_button_style.xml" qualifiers="" type="drawable"/><file name="pop_background" path="/Users/<USER>/paker1/app/src/main/res/drawable/pop_background.xml" qualifiers="" type="drawable"/><file name="default_board" path="/Users/<USER>/paker1/app/src/main/res/drawable/default_board.xml" qualifiers="" type="drawable"/><file name="course_button_style" path="/Users/<USER>/paker1/app/src/main/res/drawable/course_button_style.xml" qualifiers="" type="drawable"/><file name="default_editbox" path="/Users/<USER>/paker1/app/src/main/res/drawable/default_editbox.xml" qualifiers="" type="drawable"/><file name="ic_mtrl_chip_checked_black" path="/Users/<USER>/paker1/app/src/main/res/drawable/ic_mtrl_chip_checked_black.xml" qualifiers="" type="drawable"/><file name="shape_back_lightblue_stroke" path="/Users/<USER>/paker1/app/src/main/res/drawable/shape_back_lightblue_stroke.xml" qualifiers="" type="drawable"/><file name="img_alpha25" path="/Users/<USER>/paker1/app/src/main/res/drawable/img_alpha25.xml" qualifiers="" type="drawable"/><file name="search_backicon" path="/Users/<USER>/paker1/app/src/main/res/drawable/search_backicon.png" qualifiers="" type="drawable"/><file name="tooltip_frame_light" path="/Users/<USER>/paker1/app/src/main/res/drawable/tooltip_frame_light.xml" qualifiers="" type="drawable"/><file name="img_alpha" path="/Users/<USER>/paker1/app/src/main/res/drawable/img_alpha.png" qualifiers="" type="drawable"/><file name="search_button" path="/Users/<USER>/paker1/app/src/main/res/drawable/search_button.xml" qualifiers="" type="drawable"/><file name="circle" path="/Users/<USER>/paker1/app/src/main/res/drawable/circle.png" qualifiers="" type="drawable"/><file name="search_icon" path="/Users/<USER>/paker1/app/src/main/res/drawable/search_icon.png" qualifiers="" type="drawable"/><file name="shape_back_white_stroke" path="/Users/<USER>/paker1/app/src/main/res/drawable/shape_back_white_stroke.xml" qualifiers="" type="drawable"/><file name="ic_mtrl_chip_close_circle" path="/Users/<USER>/paker1/app/src/main/res/drawable/ic_mtrl_chip_close_circle.xml" qualifiers="" type="drawable"/><file name="social_button" path="/Users/<USER>/paker1/app/src/main/res/drawable/social_button.xml" qualifiers="" type="drawable"/><file name="img_alpha100" path="/Users/<USER>/paker1/app/src/main/res/drawable/img_alpha100.png" qualifiers="" type="drawable"/><file name="mini_call_button" path="/Users/<USER>/paker1/app/src/main/res/drawable/mini_call_button.xml" qualifiers="" type="drawable"/><file name="icon_phone1" path="/Users/<USER>/paker1/app/src/main/res/drawable/icon_phone1.png" qualifiers="" type="drawable"/><file name="tooltip_frame_dark" path="/Users/<USER>/paker1/app/src/main/res/drawable/tooltip_frame_dark.xml" qualifiers="" type="drawable"/><file name="img_alpha75" path="/Users/<USER>/paker1/app/src/main/res/drawable/img_alpha75.xml" qualifiers="" type="drawable"/><file name="signin_line" path="/Users/<USER>/paker1/app/src/main/res/drawable/signin_line.png" qualifiers="" type="drawable"/><file name="normal_button_style" path="/Users/<USER>/paker1/app/src/main/res/drawable/normal_button_style.xml" qualifiers="" type="drawable"/><file name="side_nav_bar" path="/Users/<USER>/paker1/app/src/main/res/drawable/side_nav_bar.xml" qualifiers="" type="drawable"/><file name="logo" path="/Users/<USER>/paker1/app/src/main/res/mipmap-xxxhdpi/logo.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/paker1/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/paker1/app/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="popup_blocktodaycall" path="/Users/<USER>/paker1/app/src/main/res/layout/popup_blocktodaycall.xml" qualifiers="" type="layout"/><file name="fragment_main" path="/Users/<USER>/paker1/app/src/main/res/layout/fragment_main.xml" qualifiers="" type="layout"/><file name="adapter_noticelist" path="/Users/<USER>/paker1/app/src/main/res/layout/adapter_noticelist.xml" qualifiers="" type="layout"/><file name="fragment_block" path="/Users/<USER>/paker1/app/src/main/res/layout/fragment_block.xml" qualifiers="" type="layout"/><file name="content_main" path="/Users/<USER>/paker1/app/src/main/res/layout/content_main.xml" qualifiers="" type="layout"/><file name="fragment_block_history" path="/Users/<USER>/paker1/app/src/main/res/layout/fragment_block_history.xml" qualifiers="" type="layout"/><file name="activity_login" path="/Users/<USER>/paker1/app/src/main/res/layout/activity_login.xml" qualifiers="" type="layout"/><file name="popup_blockcallexp" path="/Users/<USER>/paker1/app/src/main/res/layout/popup_blockcallexp.xml" qualifiers="" type="layout"/><file name="adapter_drawer" path="/Users/<USER>/paker1/app/src/main/res/layout/adapter_drawer.xml" qualifiers="" type="layout"/><file name="fragment_notice" path="/Users/<USER>/paker1/app/src/main/res/layout/fragment_notice.xml" qualifiers="" type="layout"/><file name="fragment_block_setting" path="/Users/<USER>/paker1/app/src/main/res/layout/fragment_block_setting.xml" qualifiers="" type="layout"/><file name="popup_blockunknown" path="/Users/<USER>/paker1/app/src/main/res/layout/popup_blockunknown.xml" qualifiers="" type="layout"/><file name="popup_blockprefnum" path="/Users/<USER>/paker1/app/src/main/res/layout/popup_blockprefnum.xml" qualifiers="" type="layout"/><file name="content_search_tabbar" path="/Users/<USER>/paker1/app/src/main/res/layout/content_search_tabbar.xml" qualifiers="" type="layout"/><file name="adapter_phone" path="/Users/<USER>/paker1/app/src/main/res/layout/adapter_phone.xml" qualifiers="" type="layout"/><file name="nav_header_main" path="/Users/<USER>/paker1/app/src/main/res/layout/nav_header_main.xml" qualifiers="" type="layout"/><file name="adapter_blockhistory" path="/Users/<USER>/paker1/app/src/main/res/layout/adapter_blockhistory.xml" qualifiers="" type="layout"/><file name="select_dialog_singlechoice_material" path="/Users/<USER>/paker1/app/src/main/res/layout/select_dialog_singlechoice_material.xml" qualifiers="" type="layout"/><file name="adapter_blocklist" path="/Users/<USER>/paker1/app/src/main/res/layout/adapter_blocklist.xml" qualifiers="" type="layout"/><file name="select_dialog_item_material" path="/Users/<USER>/paker1/app/src/main/res/layout/select_dialog_item_material.xml" qualifiers="" type="layout"/><file name="adapter_recentcalllist" path="/Users/<USER>/paker1/app/src/main/res/layout/adapter_recentcalllist.xml" qualifiers="" type="layout"/><file name="fragment_search" path="/Users/<USER>/paker1/app/src/main/res/layout/fragment_search.xml" qualifiers="" type="layout"/><file name="pop_up_notice" path="/Users/<USER>/paker1/app/src/main/res/layout/pop_up_notice.xml" qualifiers="" type="layout"/><file name="support_simple_spinner_dropdown_item" path="/Users/<USER>/paker1/app/src/main/res/layout/support_simple_spinner_dropdown_item.xml" qualifiers="" type="layout"/><file name="fragment_websearch" path="/Users/<USER>/paker1/app/src/main/res/layout/fragment_websearch.xml" qualifiers="" type="layout"/><file name="fragment_block_numbers" path="/Users/<USER>/paker1/app/src/main/res/layout/fragment_block_numbers.xml" qualifiers="" type="layout"/><file name="content_main_search" path="/Users/<USER>/paker1/app/src/main/res/layout/content_main_search.xml" qualifiers="" type="layout"/><file name="activity_main" path="/Users/<USER>/paker1/app/src/main/res/layout/activity_main.xml" qualifiers="" type="layout"/><file name="pop_up_window" path="/Users/<USER>/paker1/app/src/main/res/layout/pop_up_window.xml" qualifiers="" type="layout"/><file name="popup_blockspecnum" path="/Users/<USER>/paker1/app/src/main/res/layout/popup_blockspecnum.xml" qualifiers="" type="layout"/><file name="popup_block_all" path="/Users/<USER>/paker1/app/src/main/res/layout/popup_block_all.xml" qualifiers="" type="layout"/><file name="fragment_setting" path="/Users/<USER>/paker1/app/src/main/res/layout/fragment_setting.xml" qualifiers="" type="layout"/><file name="app_bar_main" path="/Users/<USER>/paker1/app/src/main/res/layout/app_bar_main.xml" qualifiers="" type="layout"/><file name="select_dialog_multichoice_material" path="/Users/<USER>/paker1/app/src/main/res/layout/select_dialog_multichoice_material.xml" qualifiers="" type="layout"/><file name="logo" path="/Users/<USER>/paker1/app/src/main/res/mipmap-xxhdpi/logo.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/paker1/app/src/main/res/mipmap-xxhdpi/ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/paker1/app/src/main/res/mipmap-xxhdpi/ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="switch_thumb_material_light" path="/Users/<USER>/paker1/app/src/main/res/color/switch_thumb_material_light.xml" qualifiers="" type="color"/><file name="switch_thumb_material_dark" path="/Users/<USER>/paker1/app/src/main/res/color/switch_thumb_material_dark.xml" qualifiers="" type="color"/><file path="/Users/<USER>/paker1/app/src/main/res/values/colors.xml" qualifiers=""><color name="Gray">#808080</color><color name="Gray_divider">#141921</color><color name="LightGray">#d0d0d0</color><color name="Login_Split">#eeeeee</color><color name="SubTitleBack">#1a1c26</color><color name="SubTitleBackLine">#242836</color><color name="back_color">#0a0d20</color><color name="back_color1">#3a3f5d</color><color name="back_color2">#303650</color><color name="back_color3">#232637</color><color name="black">#000000</color><color name="blue_default">#1590fa</color><color name="blue_light">#79bdfa</color><color name="colorAccent">#FF4081</color><color name="colorPrimary">#3F51B5</color><color name="colorPrimaryDark">#303F9F</color><color name="green_default">#4caf50</color><color name="green_light">#81c784</color><color name="orange_default">#ff9800</color><color name="orange_light">#ffb74d</color><color name="red_default">#f44336</color><color name="red_light">#e57373</color><color name="white">#ffffff</color><color name="yellow_default">#ffeb3b</color><color name="yellow_light">#fff176</color><color name="line_color">#2c2f40</color><color name="line_color1">#5c5d6b</color><color name="normal_text_color">#ffffff</color><color name="normal_text_color2">#e2e2e2</color><color name="normal_text_color3">#878787</color><color name="normal_text_color4">#303030</color><color name="normal_text_color5">#9d9d9d</color><color name="normal_text_color6">#9d9d9d</color><color name="menu_item_back_color">#2a4057</color><color name="menu_item_divider_color">#5095bf</color><color name="menu_item_sel_color">#65737c</color><color name="divider_normal_color">#e8e8e8</color><color name="greenlight_alpha_color">#40c0ffc0</color><color name="white_alpha_color">#20ffffff</color><color name="red">#ff0000</color><color name="color_sync0">#8f7bdd</color><color name="color_sync1">#c656a1</color><color name="color_sync2">#636363</color><color name="color_sync3">#e0376a</color><color name="color_sync4">#f86591</color><color name="color_sync5">#e03569</color></file><file path="/Users/<USER>/paker1/app/src/main/res/values/bools.xml" qualifiers=""/><file path="/Users/<USER>/paker1/app/src/main/res/values/dimens.xml" qualifiers=""><dimen name="activity_horizontal_margin">16dp</dimen><dimen name="activity_vertical_margin">16dp</dimen><dimen name="nav_header_vertical_spacing">8dp</dimen><dimen name="nav_header_height">176dp</dimen><dimen name="fab_margin">16dp</dimen><dimen name="_1dp">1dp</dimen><dimen name="_2dp">2dp</dimen><dimen name="_4dp">4dp</dimen><dimen name="_5dp">5dp</dimen><dimen name="_8dp">8dp</dimen><dimen name="_10dp">10dp</dimen><dimen name="_12sp">12sp</dimen><dimen name="_13sp">13sp</dimen><dimen name="_14sp">14sp</dimen><dimen name="_15dp">15dp</dimen><dimen name="_16dp">16dp</dimen><dimen name="_16sp">16sp</dimen><dimen name="_18sp">18sp</dimen><dimen name="_20dp">20dp</dimen><dimen name="_20sp">20sp</dimen><dimen name="_25dp">25dp</dimen><dimen name="_30dp">30dp</dimen><dimen name="_40dp">40dp</dimen><dimen name="_50dp">50dp</dimen><dimen name="_60dp">60dp</dimen><dimen name="_70dp">70dp</dimen><dimen name="_150dp">150dp</dimen><dimen name="_280dp">280dp</dimen></file><file path="/Users/<USER>/paker1/app/src/main/res/values/arrays.xml" qualifiers=""><array name="drawer_items">
        <item>전화번호 검색</item>
        <item>콜폭방어</item>
        <item>공지사항</item>
        <item>환경설정</item>
        <item>로그아웃</item>
    </array></file><file path="/Users/<USER>/paker1/app/src/main/res/values/styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style><style name="AppTheme.NoActionBar" parent="AppTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/><style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/><style name="login_edit">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/login_edit</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textColorHint">@color/normal_text_color3</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
    </style><style name="ToolbarColoredBackArrow" parent="AppTheme">
        <item name="android:textColorSecondary">@color/white</item>
    </style><style name="toolbarWhiteText" parent="AppTheme">
        <item name="android:textColor">@color/white</item>
        <item name="android:textColorPrimary">@color/white</item>
    </style></file><file path="/Users/<USER>/paker1/app/src/main/res/values/strings.xml" qualifiers=""><string name="abc_action_bar_home_description">Navigate home</string><string name="abc_action_bar_up_description">Navigate up</string><string name="abc_action_menu_overflow_description">More options</string><string name="abc_action_mode_done">Done</string><string name="abc_activity_chooser_view_see_all">See all</string><string name="abc_activitychooserview_choose_application">Choose an app</string><string name="abc_capital_off">OFF</string><string name="abc_capital_on">ON</string><string name="abc_font_family_body_1_material">sans-serif</string><string name="abc_font_family_body_2_material">sans-serif-medium</string><string name="abc_font_family_button_material">sans-serif-medium</string><string name="abc_font_family_caption_material">sans-serif</string><string name="abc_font_family_display_1_material">sans-serif</string><string name="abc_font_family_display_2_material">sans-serif</string><string name="abc_font_family_display_3_material">sans-serif</string><string name="abc_font_family_display_4_material">sans-serif-light</string><string name="abc_font_family_headline_material">sans-serif</string><string name="abc_font_family_menu_material">sans-serif</string><string name="abc_font_family_subhead_material">sans-serif</string><string name="abc_font_family_title_material">sans-serif-medium</string><string name="abc_menu_alt_shortcut_label">Alt+</string><string name="abc_menu_ctrl_shortcut_label">Ctrl+</string><string name="abc_menu_delete_shortcut_label">delete</string><string name="abc_menu_enter_shortcut_label">enter</string><string name="abc_menu_function_shortcut_label">Function+</string><string name="abc_menu_meta_shortcut_label">Meta+</string><string name="abc_menu_shift_shortcut_label">Shift+</string><string name="abc_menu_space_shortcut_label">space</string><string name="abc_menu_sym_shortcut_label">Sym+</string><string name="abc_prepend_shortcut_label">Menu+</string><string name="abc_search_hint">Search…</string><string name="abc_searchview_description_clear">Clear query</string><string name="abc_searchview_description_query">Search query</string><string name="abc_searchview_description_search">Search</string><string name="abc_searchview_description_submit">Submit query</string><string name="abc_searchview_description_voice">Voice search</string><string name="abc_shareactionprovider_share_with">Share with</string><string name="abc_shareactionprovider_share_with_application">Share with %s</string><string name="abc_toolbar_collapse_description">Collapse</string><string name="action_settings">Settings</string><string name="app_name">FAKER</string><string name="appbar_scrolling_view_behavior">android.support.design.widget.AppBarLayout$ScrollingViewBehavior</string><string name="blacklist_request_permissions">Request permissions</string><string name="bottom_sheet_behavior">android.support.design.widget.BottomSheetBehavior</string><string name="cancel">취소</string><string name="character_counter_content_description">Character limit exceeded %1$d of %2$d</string><string name="character_counter_pattern">%1$d / %2$d</string><string name="empty_data">등록된 정보없음</string><string name="fab_transformation_scrim_behavior">android.support.design.transformation.FabTransformationScrimBehavior</string><string name="fab_transformation_sheet_behavior">android.support.design.transformation.FabTransformationSheetBehavior</string><string name="hello_blank_fragment">Hello blank fragment</string><string name="hide_bottom_view_on_scroll_behavior">android.support.design.behavior.HideBottomViewOnScrollBehavior</string><string name="mtrl_chip_close_icon_content_description">Remove %1$s</string><string name="navigation_drawer_close">Close navigation drawer</string><string name="navigation_drawer_open">Open navigation drawer</string><string name="noCallHistory">콜/SMS이력 없음</string><string name="noData">-</string><string name="password_toggle_content_description">Show password</string><string name="path_password_eye">M12,4.5C7,4.5 2.73,7.61 1,12c1.73,4.39 6,7.5 11,7.5s9.27,-3.11 11,-7.5c-1.73,-4.39 -6,-7.5 -11,-7.5zM12,17c-2.76,0 -5,-2.24 -5,-5s2.24,-5 5,-5 5,2.24 5,5 -2.24,5 -5,5zM12,9c-1.66,0 -3,1.34 -3,3s1.34,3 3,3 3,-1.34 3,-3 -1.34,-3 -3,-3z</string><string name="path_password_eye_mask_strike_through">M2,4.27 L19.73,22 L22.27,19.46 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string><string name="path_password_eye_mask_visible">M2,4.27 L2,4.27 L4.54,1.73 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string><string name="path_password_strike_through">M3.27,4.27 L19.74,20.74</string><string name="permissions_required">퍼미션을 모두 허용해주셔야 이 앱을 이용하실수 있습니다.</string><string name="phonesearch_item">전화번호 검색</string><string name="popup_position">팝업창 위치</string><string name="question_item">문의사항</string><string name="report_item">공지사항</string><string name="search_hint">전화번호를 입력하세요</string><string name="search_menu_title">Search</string><string name="service_fail">데이터 신호 약함</string><string name="setting">설정</string><string name="setting_IPlabel">IP주소</string><string name="setting_Portlabel">Port주소</string><string name="setting_title">서버설정</string><string name="status_bar_notification_info_overflow">999+</string><string name="tab_txt_name">FAKER</string><string name="tab_txt_number">전화번호: 010-2344-4517</string><string name="text_cancel">취소하기</string><string name="text_ok">확인</string><string name="title_phoneNumber">전화번호검색</string><string name="today_call_request">오늘 전화문의 보기</string><string name="user_id_hint">아이디</string><string name="user_login">로그인</string><string name="user_login_title">로그인정보</string><string name="user_pass_hint">패스워드</string><string name="wait">잠시만 기다려주세요...</string></file><file path="/Users/<USER>/paker1/app/src/main/res/values/integers.xml" qualifiers=""><integer name="status_bar_notification_info_maxnum">999</integer></file><file path="/Users/<USER>/paker1/app/src/main/res/values/drawables.xml" qualifiers=""><drawable name="ic_menu_camera">@android:drawable/ic_menu_camera</drawable><drawable name="ic_menu_gallery">@android:drawable/ic_menu_gallery</drawable><drawable name="ic_menu_manage">@android:drawable/ic_menu_manage</drawable><drawable name="ic_menu_send">@android:drawable/ic_menu_send</drawable><drawable name="ic_menu_share">@android:drawable/ic_menu_share</drawable><drawable name="ic_menu_slideshow">@android:drawable/ic_menu_slideshow</drawable><drawable name="notification_template_icon_bg">#3333b5e5</drawable><drawable name="notification_template_icon_low_bg">#0cffffff</drawable></file><file name="notify_panel_notification_icon_bg" path="/Users/<USER>/paker1/app/src/main/res/drawable-xhdpi/notify_panel_notification_icon_bg.png" qualifiers="xhdpi-v4" type="drawable"/><file name="telegram_icon" path="/Users/<USER>/paker1/app/src/main/res/drawable-xxhdpi/telegram_icon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="search_back1" path="/Users/<USER>/paker1/app/src/main/res/drawable-xxhdpi/search_back1.PNG" qualifiers="xxhdpi-v4" type="drawable"/><file name="login_back" path="/Users/<USER>/paker1/app/src/main/res/drawable-xxhdpi/login_back.PNG" qualifiers="xxhdpi-v4" type="drawable"/><file name="icon2" path="/Users/<USER>/paker1/app/src/main/res/drawable-xxhdpi/icon2.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="search_back" path="/Users/<USER>/paker1/app/src/main/res/drawable-xxhdpi/search_back.PNG" qualifiers="xxhdpi-v4" type="drawable"/><file name="img_back2" path="/Users/<USER>/paker1/app/src/main/res/drawable-xxhdpi/img_back2.PNG" qualifiers="xxhdpi-v4" type="drawable"/><file name="icon_nodata" path="/Users/<USER>/paker1/app/src/main/res/drawable-xxhdpi/icon_nodata.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="provider_paths" path="/Users/<USER>/paker1/app/src/main/res/xml/provider_paths.xml" qualifiers="" type="xml"/><file name="notify_panel_notification_icon_bg" path="/Users/<USER>/paker1/app/src/main/res/drawable-hdpi/notify_panel_notification_icon_bg.png" qualifiers="hdpi-v4" type="drawable"/><file name="icon_miss" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_miss.PNG" qualifiers="v21" type="drawable"/><file name="icon_9" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_9.png" qualifiers="v21" type="drawable"/><file name="icon_none" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_none.png" qualifiers="v21" type="drawable"/><file name="avd_hide_password" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/avd_hide_password.xml" qualifiers="v21" type="drawable"/><file name="icon_8" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_8.PNG" qualifiers="v21" type="drawable"/><file name="_avd_hide_password__2_res_0x7f080002" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/_avd_hide_password__2_res_0x7f080002.xml" qualifiers="v21" type="drawable"/><file name="_avd_hide_password__1_res_0x7f080001" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/_avd_hide_password__1_res_0x7f080001.xml" qualifiers="v21" type="drawable"/><file name="_avd_show_password__0_res_0x7f080003" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/_avd_show_password__0_res_0x7f080003.xml" qualifiers="v21" type="drawable"/><file name="icon_11" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_11.png" qualifiers="v21" type="drawable"/><file name="icon_10" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_10.png" qualifiers="v21" type="drawable"/><file name="icon_accept" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_accept.PNG" qualifiers="v21" type="drawable"/><file name="icon_12" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_12.png" qualifiers="v21" type="drawable"/><file name="_avd_show_password__2_res_0x7f080005" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/_avd_show_password__2_res_0x7f080005.xml" qualifiers="v21" type="drawable"/><file name="_avd_show_password__1_res_0x7f080004" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/_avd_show_password__1_res_0x7f080004.xml" qualifiers="v21" type="drawable"/><file name="avd_show_password" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/avd_show_password.xml" qualifiers="v21" type="drawable"/><file name="_avd_hide_password__0_res_0x7f080000" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/_avd_hide_password__0_res_0x7f080000.xml" qualifiers="v21" type="drawable"/><file name="icon_reject" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_reject.PNG" qualifiers="v21" type="drawable"/><file name="icon_0" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_0.PNG" qualifiers="v21" type="drawable"/><file name="icon_1" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_1.PNG" qualifiers="v21" type="drawable"/><file name="icon_3" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_3.PNG" qualifiers="v21" type="drawable"/><file name="icon_2" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_2.PNG" qualifiers="v21" type="drawable"/><file name="icon_6" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_6.PNG" qualifiers="v21" type="drawable"/><file name="icon_sms" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_sms.png" qualifiers="v21" type="drawable"/><file name="icon_7" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_7.PNG" qualifiers="v21" type="drawable"/><file name="icon_5" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_5.PNG" qualifiers="v21" type="drawable"/><file name="icon_4" path="/Users/<USER>/paker1/app/src/main/res/drawable-v21/icon_4.PNG" qualifiers="v21" type="drawable"/><file name="main" path="/Users/<USER>/paker1/app/src/main/res/menu/main.xml" qualifiers="" type="menu"/><file name="notify_panel_notification_icon_bg" path="/Users/<USER>/paker1/app/src/main/res/drawable-mdpi/notify_panel_notification_icon_bg.png" qualifiers="mdpi-v4" type="drawable"/><file name="logo" path="/Users/<USER>/paker1/app/src/main/res/mipmap-xhdpi/logo.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/paker1/app/src/main/res/mipmap-xhdpi/ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/Users/<USER>/paker1/app/src/main/res/mipmap-xhdpi/ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/paker1/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/paker1/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/paker1/app/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/paker1/app/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>