{"logs": [{"outputFile": "com.developer.faker.app-mergeDebugResources-41:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/res/values-es-rUS/values-es-rUS.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "9015", "endColumns": "100", "endOffsets": "9111"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/32abaf143a115377682b07bc710faabb/transformed/navigation-ui-2.7.5/res/values-es-rUS/values-es-rUS.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,127", "endOffsets": "165,293"}, "to": {"startLines": "100,101", "startColumns": "4,4", "startOffsets": "8606,8721", "endColumns": "114,127", "endOffsets": "8716,8844"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/b3a61cce2ad1dad98c4e5ab92fec4e8c/transformed/material-1.10.0/res/values-es-rUS/values-es-rUS.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,360,440,526,631,727,829,957,1038,1103,1198,1268,1331,1424,1488,1560,1623,1697,1761,1817,1935,1993,2055,2111,2191,2325,2414,2495,2636,2717,2797,2948,3038,3115,3171,3227,3293,3369,3451,3539,3628,3701,3778,3848,3925,4031,4120,4194,4288,4390,4462,4543,4647,4700,4785,4852,4945,5034,5096,5160,5223,5291,5402,5513,5615,5720,5780,5840", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,81,79,85,104,95,101,127,80,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,80,140,80,79,150,89,76,55,55,65,75,81,87,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82", "endOffsets": "273,355,435,521,626,722,824,952,1033,1098,1193,1263,1326,1419,1483,1555,1618,1692,1756,1812,1930,1988,2050,2106,2186,2320,2409,2490,2631,2712,2792,2943,3033,3110,3166,3222,3288,3364,3446,3534,3623,3696,3773,3843,3920,4026,4115,4189,4283,4385,4457,4538,4642,4695,4780,4847,4940,5029,5091,5155,5218,5286,5397,5508,5610,5715,5775,5835,5918"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3044,3126,3206,3292,3397,3493,3595,3723,3804,3869,3964,4034,4097,4190,4254,4326,4389,4463,4527,4583,4701,4759,4821,4877,4957,5091,5180,5261,5402,5483,5563,5714,5804,5881,5937,5993,6059,6135,6217,6305,6394,6467,6544,6614,6691,6797,6886,6960,7054,7156,7228,7309,7413,7466,7551,7618,7711,7800,7862,7926,7989,8057,8168,8279,8381,8486,8546,8849", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,102", "endColumns": "12,81,79,85,104,95,101,127,80,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,80,140,80,79,150,89,76,55,55,65,75,81,87,88,72,76,69,76,105,88,73,93,101,71,80,103,52,84,66,92,88,61,63,62,67,110,110,101,104,59,59,82", "endOffsets": "323,3121,3201,3287,3392,3488,3590,3718,3799,3864,3959,4029,4092,4185,4249,4321,4384,4458,4522,4578,4696,4754,4816,4872,4952,5086,5175,5256,5397,5478,5558,5709,5799,5876,5932,5988,6054,6130,6212,6300,6389,6462,6539,6609,6686,6792,6881,6955,7049,7151,7223,7304,7408,7461,7546,7613,7706,7795,7857,7921,7984,8052,8163,8274,8376,8481,8541,8601,8927"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/9f4ccd3212b97d78ca5085eb75c4d8ac/transformed/appcompat-1.6.1/res/values-es-rUS/values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,448,557,665,750,852,968,1053,1133,1224,1317,1412,1506,1605,1698,1797,1893,1984,2075,2157,2264,2363,2462,2570,2678,2785,2944,8932", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "443,552,660,745,847,963,1048,1128,1219,1312,1407,1501,1600,1693,1792,1888,1979,2070,2152,2259,2358,2457,2565,2673,2780,2939,3039,9010"}}]}]}