{"logs": [{"outputFile": "com.developer.faker.app-mergeDebugResources-41:/values-sk/values-sk.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/9f4ccd3212b97d78ca5085eb75c4d8ac/transformed/appcompat-1.6.1/res/values-sk/values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "428,535,636,747,833,941,1059,1138,1215,1306,1399,1497,1591,1691,1784,1879,1977,2068,2159,2243,2348,2456,2555,2661,2773,2876,3042,8825", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "530,631,742,828,936,1054,1133,1210,1301,1394,1492,1586,1686,1779,1874,1972,2063,2154,2238,2343,2451,2550,2656,2768,2871,3037,3135,8903"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/32abaf143a115377682b07bc710faabb/transformed/navigation-ui-2.7.5/res/values-sk/values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,126", "endOffsets": "156,283"}, "to": {"startLines": "102,103", "startColumns": "4,4", "startOffsets": "8513,8619", "endColumns": "105,126", "endOffsets": "8614,8741"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/res/values-sk/values-sk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "8908", "endColumns": "100", "endOffsets": "9004"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/b3a61cce2ad1dad98c4e5ab92fec4e8c/transformed/material-1.10.0/res/values-sk/values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,378,453,528,606,698,781,873,1001,1082,1147,1246,1322,1387,1477,1541,1607,1661,1730,1790,1844,1961,2021,2083,2137,2209,2339,2426,2518,2657,2726,2804,2935,3023,3103,3157,3208,3274,3346,3423,3506,3588,3660,3737,3810,3881,3986,4074,4146,4238,4334,4408,4482,4578,4630,4712,4779,4866,4953,5015,5079,5142,5210,5316,5423,5521,5638,5696,5751", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,74,74,77,91,82,91,127,80,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,91,138,68,77,130,87,79,53,50,65,71,76,82,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78", "endOffsets": "373,448,523,601,693,776,868,996,1077,1142,1241,1317,1382,1472,1536,1602,1656,1725,1785,1839,1956,2016,2078,2132,2204,2334,2421,2513,2652,2721,2799,2930,3018,3098,3152,3203,3269,3341,3418,3501,3583,3655,3732,3805,3876,3981,4069,4141,4233,4329,4403,4477,4573,4625,4707,4774,4861,4948,5010,5074,5137,5205,5311,5418,5516,5633,5691,5746,5825"}, "to": {"startLines": "2,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3140,3215,3290,3368,3460,3543,3635,3763,3844,3909,4008,4084,4149,4239,4303,4369,4423,4492,4552,4606,4723,4783,4845,4899,4971,5101,5188,5280,5419,5488,5566,5697,5785,5865,5919,5970,6036,6108,6185,6268,6350,6422,6499,6572,6643,6748,6836,6908,7000,7096,7170,7244,7340,7392,7474,7541,7628,7715,7777,7841,7904,7972,8078,8185,8283,8400,8458,8746", "endLines": "7,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,104", "endColumns": "12,74,74,77,91,82,91,127,80,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,91,138,68,77,130,87,79,53,50,65,71,76,82,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78", "endOffsets": "423,3210,3285,3363,3455,3538,3630,3758,3839,3904,4003,4079,4144,4234,4298,4364,4418,4487,4547,4601,4718,4778,4840,4894,4966,5096,5183,5275,5414,5483,5561,5692,5780,5860,5914,5965,6031,6103,6180,6263,6345,6417,6494,6567,6638,6743,6831,6903,6995,7091,7165,7239,7335,7387,7469,7536,7623,7710,7772,7836,7899,7967,8073,8180,8278,8395,8453,8508,8820"}}]}]}