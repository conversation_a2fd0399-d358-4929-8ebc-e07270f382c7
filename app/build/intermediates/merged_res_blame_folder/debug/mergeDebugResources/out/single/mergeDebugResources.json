[{"merged": "com.developer.faker.app-debug-43:/drawable_social_button.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/social_button.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_11.png.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_11.png"}, {"merged": "com.developer.faker.app-debug-43:/layout_nav_header_main.xml.flat", "source": "com.developer.faker.app-main-45:/layout/nav_header_main.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable_side_nav_bar.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/side_nav_bar.xml"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-mdpi_social_line.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-mdpi/social_line.png"}, {"merged": "com.developer.faker.app-debug-43:/color_switch_thumb_material_light.xml.flat", "source": "com.developer.faker.app-main-45:/color/switch_thumb_material_light.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_pop_up_notice.xml.flat", "source": "com.developer.faker.app-main-45:/layout/pop_up_notice.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_5.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_5.PNG"}, {"merged": "com.developer.faker.app-debug-43:/menu_main.xml.flat", "source": "com.developer.faker.app-main-45:/menu/main.xml"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-mdpi_settings.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-mdpi/settings.png"}, {"merged": "com.developer.faker.app-debug-43:/anim_translate.xml.flat", "source": "com.developer.faker.app-main-45:/anim/translate.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable_img_alpha100.png.flat", "source": "com.developer.faker.app-main-45:/drawable/img_alpha100.png"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-mdpi_social_facebook.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-mdpi/social_facebook.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable_navigation_empty_icon.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/navigation_empty_icon.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable_img_alpha50.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/img_alpha50.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-xxhdpi_img_back2.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-xxhdpi/img_back2.PNG"}, {"merged": "com.developer.faker.app-debug-43:/drawable_course_button_style.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/course_button_style.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_activity_main.xml.flat", "source": "com.developer.faker.app-main-45:/layout/activity_main.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v23_login_edit.xml.flat", "source": "com.developer.faker.app-main-45:/drawable-v23/login_edit.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_10.png.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_10.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable_img_alpha1.png.flat", "source": "com.developer.faker.app-main-45:/drawable/img_alpha1.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-xhdpi_notify_panel_notification_icon_bg.png.flat", "source": "com.developer.faker.app-main-45:/drawable-xhdpi/notify_panel_notification_icon_bg.png"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-xhdpi_ic_launcher_round.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-xhdpi/ic_launcher_round.png"}, {"merged": "com.developer.faker.app-debug-43:/layout_select_dialog_singlechoice_material.xml.flat", "source": "com.developer.faker.app-main-45:/layout/select_dialog_singlechoice_material.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable_search_button.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/search_button.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_6.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_6.PNG"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.developer.faker.app-debug-43:/layout_fragment_block_setting.xml.flat", "source": "com.developer.faker.app-main-45:/layout/fragment_block_setting.xml"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-mdpi_arrow_right.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-mdpi/arrow_right.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v23_login_mark_email.png.flat", "source": "com.developer.faker.app-main-45:/drawable-v23/login_mark_email.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_9.png.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_9.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable_mini_call_button.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/mini_call_button.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_activity_login.xml.flat", "source": "com.developer.faker.app-main-45:/layout/activity_login.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_3.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_3.PNG"}, {"merged": "com.developer.faker.app-debug-43:/drawable-xxhdpi_search_back1.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-xxhdpi/search_back1.PNG"}, {"merged": "com.developer.faker.app-debug-43:/drawable_normal_button_style.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/normal_button_style.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_fragment_block_history.xml.flat", "source": "com.developer.faker.app-main-45:/layout/fragment_block_history.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_adapter_noticelist.xml.flat", "source": "com.developer.faker.app-main-45:/layout/adapter_noticelist.xml"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v23_pop_reject.png.flat", "source": "com.developer.faker.app-main-45:/drawable-v23/pop_reject.png"}, {"merged": "com.developer.faker.app-debug-43:/layout_adapter_blocklist.xml.flat", "source": "com.developer.faker.app-main-45:/layout/adapter_blocklist.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21__avd_show_password__2_res_0x7f080005.xml.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/_avd_show_password__2_res_0x7f080005.xml"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable_shape_back_lightblue_stroke.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/shape_back_lightblue_stroke.xml"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-mdpi_line.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-mdpi/line.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_sms.png.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_sms.png"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-hdpi_logo.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-hdpi/logo.png"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-mdpi_notify_panel_notification_icon_bg.png.flat", "source": "com.developer.faker.app-main-45:/drawable-mdpi/notify_panel_notification_icon_bg.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v23_pop_back.png.flat", "source": "com.developer.faker.app-main-45:/drawable-v23/pop_back.png"}, {"merged": "com.developer.faker.app-debug-43:/layout_adapter_drawer.xml.flat", "source": "com.developer.faker.app-main-45:/layout/adapter_drawer.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_avd_hide_password.xml.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/avd_hide_password.xml"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-mdpi_ic_launcher_round.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-mdpi/ic_launcher_round.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_12.png.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_12.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_2.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_2.PNG"}, {"merged": "com.developer.faker.app-debug-43:/drawable_main_title.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/main_title.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable_img_alpha25.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/img_alpha25.xml"}, {"merged": "com.developer.faker.app-debug-43:/anim_bounce.xml.flat", "source": "com.developer.faker.app-main-45:/anim/bounce.xml"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-mdpi_logo.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-mdpi/logo.png"}, {"merged": "com.developer.faker.app-debug-43:/layout_content_main_search.xml.flat", "source": "com.developer.faker.app-main-45:/layout/content_main_search.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_fragment_block_numbers.xml.flat", "source": "com.developer.faker.app-main-45:/layout/fragment_block_numbers.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_app_bar_main.xml.flat", "source": "com.developer.faker.app-main-45:/layout/app_bar_main.xml"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-xxhdpi_logo.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-xxhdpi/logo.png"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-mdpi_social_call.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-mdpi/social_call.png"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-xhdpi_logo.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-xhdpi/logo.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable_search_backicon.png.flat", "source": "com.developer.faker.app-main-45:/drawable/search_backicon.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v23_round_button.xml.flat", "source": "com.developer.faker.app-main-45:/drawable-v23/round_button.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_fragment_websearch.xml.flat", "source": "com.developer.faker.app-main-45:/layout/fragment_websearch.xml"}, {"merged": "com.developer.faker.app-debug-43:/color_switch_thumb_material_dark.xml.flat", "source": "com.developer.faker.app-main-45:/color/switch_thumb_material_dark.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_adapter_blockhistory.xml.flat", "source": "com.developer.faker.app-main-45:/layout/adapter_blockhistory.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-xxhdpi_telegram_icon.png.flat", "source": "com.developer.faker.app-main-45:/drawable-xxhdpi/telegram_icon.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_8.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_8.PNG"}, {"merged": "com.developer.faker.app-debug-43:/anim_alpha.xml.flat", "source": "com.developer.faker.app-main-45:/anim/alpha.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_accept.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_accept.PNG"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v23_icon_search_mark.png.flat", "source": "com.developer.faker.app-main-45:/drawable-v23/icon_search_mark.png"}, {"merged": "com.developer.faker.app-debug-43:/layout_adapter_recentcalllist.xml.flat", "source": "com.developer.faker.app-main-45:/layout/adapter_recentcalllist.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_fragment_main.xml.flat", "source": "com.developer.faker.app-main-45:/layout/fragment_main.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_select_dialog_item_material.xml.flat", "source": "com.developer.faker.app-main-45:/layout/select_dialog_item_material.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_1.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_1.PNG"}, {"merged": "com.developer.faker.app-debug-43:/layout_popup_blocktodaycall.xml.flat", "source": "com.developer.faker.app-main-45:/layout/popup_blocktodaycall.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21__avd_show_password__0_res_0x7f080003.xml.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/_avd_show_password__0_res_0x7f080003.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable_img_alpha75.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/img_alpha75.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_pop_up_window.xml.flat", "source": "com.developer.faker.app-main-45:/layout/pop_up_window.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_adapter_phone.xml.flat", "source": "com.developer.faker.app-main-45:/layout/adapter_phone.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v23_pop_mark.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v23/pop_mark.PNG"}, {"merged": "com.developer.faker.app-debug-43:/layout_support_simple_spinner_dropdown_item.xml.flat", "source": "com.developer.faker.app-main-45:/layout/support_simple_spinner_dropdown_item.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable_tooltip_frame_light.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/tooltip_frame_light.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable_default_editbox.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/default_editbox.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_popup_blockspecnum.xml.flat", "source": "com.developer.faker.app-main-45:/layout/popup_blockspecnum.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v23_icon_close.png.flat", "source": "com.developer.faker.app-main-45:/drawable-v23/icon_close.png"}, {"merged": "com.developer.faker.app-debug-43:/anim_slidein.xml.flat", "source": "com.developer.faker.app-main-45:/anim/slidein.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-xxhdpi_login_back.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-xxhdpi/login_back.PNG"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_avd_show_password.xml.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/avd_show_password.xml"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-mdpi_social_kakaotalk.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-mdpi/social_kakaotalk.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable_search_icon.png.flat", "source": "com.developer.faker.app-main-45:/drawable/search_icon.png"}, {"merged": "com.developer.faker.app-debug-43:/layout_fragment_search.xml.flat", "source": "com.developer.faker.app-main-45:/layout/fragment_search.xml"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-xxhdpi_ic_launcher_round.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-xxhdpi/ic_launcher_round.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21__avd_hide_password__1_res_0x7f080001.xml.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/_avd_hide_password__1_res_0x7f080001.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v23_pop_close.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v23/pop_close.PNG"}, {"merged": "com.developer.faker.app-debug-43:/layout_content_search_tabbar.xml.flat", "source": "com.developer.faker.app-main-45:/layout/content_search_tabbar.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-xxhdpi_search_back.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-xxhdpi/search_back.PNG"}, {"merged": "com.developer.faker.app-debug-43:/layout_fragment_setting.xml.flat", "source": "com.developer.faker.app-main-45:/layout/fragment_setting.xml"}, {"merged": "com.developer.faker.app-debug-43:/anim_slideout.xml.flat", "source": "com.developer.faker.app-main-45:/anim/slideout.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_popup_blockunknown.xml.flat", "source": "com.developer.faker.app-main-45:/layout/popup_blockunknown.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable_shape_back_white_stroke.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/shape_back_white_stroke.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_0.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_0.PNG"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v23_pop_result.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v23/pop_result.PNG"}, {"merged": "com.developer.faker.app-debug-43:/drawable_ic_mtrl_chip_checked_circle.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/ic_mtrl_chip_checked_circle.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v23_icon_search.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v23/icon_search.PNG"}, {"merged": "com.developer.faker.app-debug-43:/drawable_icon_phone1.png.flat", "source": "com.developer.faker.app-main-45:/drawable/icon_phone1.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable_circle.png.flat", "source": "com.developer.faker.app-main-45:/drawable/circle.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_reject.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_reject.PNG"}, {"merged": "com.developer.faker.app-debug-43:/layout_popup_blockprefnum.xml.flat", "source": "com.developer.faker.app-main-45:/layout/popup_blockprefnum.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21__avd_hide_password__0_res_0x7f080000.xml.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/_avd_hide_password__0_res_0x7f080000.xml"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-mdpi_social_google.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-mdpi/social_google.png"}, {"merged": "com.developer.faker.app-debug-43:/layout_select_dialog_multichoice_material.xml.flat", "source": "com.developer.faker.app-main-45:/layout/select_dialog_multichoice_material.xml"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-hdpi_ic_launcher_round.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-hdpi/ic_launcher_round.png"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-mdpi_call.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-mdpi/call.png"}, {"merged": "com.developer.faker.app-debug-43:/layout_fragment_block.xml.flat", "source": "com.developer.faker.app-main-45:/layout/fragment_block.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_popup_blockcallexp.xml.flat", "source": "com.developer.faker.app-main-45:/layout/popup_blockcallexp.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_content_main.xml.flat", "source": "com.developer.faker.app-main-45:/layout/content_main.xml"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-xxxhdpi_ic_launcher_round.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-xxxhdpi/ic_launcher_round.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable_pop_background.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/pop_background.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v23_login_mark_pwd.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v23/login_mark_pwd.PNG"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v23_icon_block.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v23/icon_block.PNG"}, {"merged": "com.developer.faker.app-debug-43:/xml_provider_paths.xml.flat", "source": "com.developer.faker.app-main-45:/xml/provider_paths.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v23_pop_close_gray.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v23/pop_close_gray.PNG"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v23_pop_accept.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v23/pop_accept.PNG"}, {"merged": "com.developer.faker.app-debug-43:/layout_popup_block_all.xml.flat", "source": "com.developer.faker.app-main-45:/layout/popup_block_all.xml"}, {"merged": "com.developer.faker.app-debug-43:/layout_fragment_notice.xml.flat", "source": "com.developer.faker.app-main-45:/layout/fragment_notice.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_miss.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_miss.PNG"}, {"merged": "com.developer.faker.app-debug-43:/drawable_default_board.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/default_board.xml"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-mdpi_email.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-mdpi/email.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-xxhdpi_icon_nodata.png.flat", "source": "com.developer.faker.app-main-45:/drawable-xxhdpi/icon_nodata.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_7.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_7.PNG"}, {"merged": "com.developer.faker.app-debug-43:/drawable-xxhdpi_icon2.png.flat", "source": "com.developer.faker.app-main-45:/drawable-xxhdpi/icon2.png"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-mdpi_social_sms.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-mdpi/social_sms.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable_ic_mtrl_chip_close_circle.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/ic_mtrl_chip_close_circle.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable_ic_mtrl_chip_checked_black.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/ic_mtrl_chip_checked_black.xml"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-mdpi_android_call.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-mdpi/android_call.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21__avd_show_password__1_res_0x7f080004.xml.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/_avd_show_password__1_res_0x7f080004.xml"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-mdpi_android_question.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-mdpi/android_question.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v23_icon_mail.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v23/icon_mail.PNG"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_4.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_4.PNG"}, {"merged": "com.developer.faker.app-debug-43:/drawable_signin_line.png.flat", "source": "com.developer.faker.app-main-45:/drawable/signin_line.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable_recall_button_style.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/recall_button_style.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable_img_alpha.png.flat", "source": "com.developer.faker.app-main-45:/drawable/img_alpha.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21_icon_none.png.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/icon_none.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable_img_alpha0.png.flat", "source": "com.developer.faker.app-main-45:/drawable/img_alpha0.png"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-mdpi_nav_cnt_back.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-mdpi/nav_cnt_back.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v23_icon_logout.PNG.flat", "source": "com.developer.faker.app-main-45:/drawable-v23/icon_logout.PNG"}, {"merged": "com.developer.faker.app-debug-43:/drawable_tooltip_frame_dark.xml.flat", "source": "com.developer.faker.app-main-45:/drawable/tooltip_frame_dark.xml"}, {"merged": "com.developer.faker.app-debug-43:/drawable-hdpi_notify_panel_notification_icon_bg.png.flat", "source": "com.developer.faker.app-main-45:/drawable-hdpi/notify_panel_notification_icon_bg.png"}, {"merged": "com.developer.faker.app-debug-43:/mipmap-xxxhdpi_logo.png.flat", "source": "com.developer.faker.app-main-45:/mipmap-xxxhdpi/logo.png"}, {"merged": "com.developer.faker.app-debug-43:/drawable-v21__avd_hide_password__2_res_0x7f080002.xml.flat", "source": "com.developer.faker.app-main-45:/drawable-v21/_avd_hide_password__2_res_0x7f080002.xml"}]