{"logs": [{"outputFile": "com.developer.faker.app-mergeReleaseResources-41:/values-fa/values-fa.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/b3a61cce2ad1dad98c4e5ab92fec4e8c/transformed/material-1.10.0/res/values-fa/values-fa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,334,411,493,586,673,770,899,983,1046,1136,1205,1265,1356,1419,1483,1542,1609,1671,1726,1849,1907,1968,2023,2095,2232,2313,2395,2525,2599,2673,2805,2891,2968,3019,3073,3139,3210,3287,3368,3447,3520,3594,3664,3738,3839,3925,3999,4088,4180,4254,4327,4416,4467,4547,4614,4697,4781,4843,4907,4970,5039,5133,5234,5327,5425,5480,5538", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,74,76,81,92,86,96,128,83,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,81,129,73,73,131,85,76,50,53,65,70,76,80,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77", "endOffsets": "254,329,406,488,581,668,765,894,978,1041,1131,1200,1260,1351,1414,1478,1537,1604,1666,1721,1844,1902,1963,2018,2090,2227,2308,2390,2520,2594,2668,2800,2886,2963,3014,3068,3134,3205,3282,3363,3442,3515,3589,3659,3733,3834,3920,3994,4083,4175,4249,4322,4411,4462,4542,4609,4692,4776,4838,4902,4965,5034,5128,5229,5322,5420,5475,5533,5611"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2999,3074,3151,3233,3326,3413,3510,3639,3723,3786,3876,3945,4005,4096,4159,4223,4282,4349,4411,4466,4589,4647,4708,4763,4835,4972,5053,5135,5265,5339,5413,5545,5631,5708,5759,5813,5879,5950,6027,6108,6187,6260,6334,6404,6478,6579,6665,6739,6828,6920,6994,7067,7156,7207,7287,7354,7437,7521,7583,7647,7710,7779,7873,7974,8067,8165,8220,8502", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,102", "endColumns": "12,74,76,81,92,86,96,128,83,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,81,129,73,73,131,85,76,50,53,65,70,76,80,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77", "endOffsets": "304,3069,3146,3228,3321,3408,3505,3634,3718,3781,3871,3940,4000,4091,4154,4218,4277,4344,4406,4461,4584,4642,4703,4758,4830,4967,5048,5130,5260,5334,5408,5540,5626,5703,5754,5808,5874,5945,6022,6103,6182,6255,6329,6399,6473,6574,6660,6734,6823,6915,6989,7062,7151,7202,7282,7349,7432,7516,7578,7642,7705,7774,7868,7969,8062,8160,8215,8273,8575"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/res/values-fa/values-fa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "8662", "endColumns": "100", "endOffsets": "8758"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/32abaf143a115377682b07bc710faabb/transformed/navigation-ui-2.7.5/res/values-fa/values-fa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,114", "endOffsets": "159,274"}, "to": {"startLines": "100,101", "startColumns": "4,4", "startOffsets": "8278,8387", "endColumns": "108,114", "endOffsets": "8382,8497"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/9f4ccd3212b97d78ca5085eb75c4d8ac/transformed/appcompat-1.6.1/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,419,520,631,715,816,931,1011,1088,1181,1276,1368,1462,1564,1659,1756,1850,1943,2033,2115,2223,2327,2425,2531,2636,2741,2898,8580", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "414,515,626,710,811,926,1006,1083,1176,1271,1363,1457,1559,1654,1751,1845,1938,2028,2110,2218,2322,2420,2526,2631,2736,2893,2994,8657"}}]}]}