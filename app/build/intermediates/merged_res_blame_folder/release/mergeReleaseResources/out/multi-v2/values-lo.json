{"logs": [{"outputFile": "com.developer.faker.app-mergeReleaseResources-41:/values-lo/values-lo.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/32abaf143a115377682b07bc710faabb/transformed/navigation-ui-2.7.5/res/values-lo/values-lo.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,109", "endOffsets": "159,269"}, "to": {"startLines": "100,101", "startColumns": "4,4", "startOffsets": "8266,8375", "endColumns": "108,109", "endOffsets": "8370,8480"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/res/values-lo/values-lo.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "8646", "endColumns": "100", "endOffsets": "8742"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/9f4ccd3212b97d78ca5085eb75c4d8ac/transformed/appcompat-1.6.1/res/values-lo/values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,422,525,638,723,827,938,1016,1093,1184,1277,1369,1463,1563,1656,1751,1847,1938,2029,2110,2217,2321,2419,2522,2626,2730,2887,8564", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "417,520,633,718,822,933,1011,1088,1179,1272,1364,1458,1558,1651,1746,1842,1933,2024,2105,2212,2316,2414,2517,2621,2725,2882,2981,8641"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/b3a61cce2ad1dad98c4e5ab92fec4e8c/transformed/material-1.10.0/res/values-lo/values-lo.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,343,414,495,581,664,779,898,981,1047,1136,1205,1264,1359,1425,1490,1548,1613,1674,1734,1840,1901,1961,2019,2090,2209,2295,2377,2520,2595,2671,2802,2892,2970,3025,3080,3146,3215,3289,3368,3447,3520,3597,3666,3736,3833,3918,3993,4086,4179,4253,4322,4416,4468,4551,4618,4702,4786,4848,4912,4975,5045,5144,5242,5337,5431,5490,5549", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,73,70,80,85,82,114,118,82,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,81,142,74,75,130,89,77,54,54,65,68,73,78,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78", "endOffsets": "264,338,409,490,576,659,774,893,976,1042,1131,1200,1259,1354,1420,1485,1543,1608,1669,1729,1835,1896,1956,2014,2085,2204,2290,2372,2515,2590,2666,2797,2887,2965,3020,3075,3141,3210,3284,3363,3442,3515,3592,3661,3731,3828,3913,3988,4081,4174,4248,4317,4411,4463,4546,4613,4697,4781,4843,4907,4970,5040,5139,5237,5332,5426,5485,5544,5623"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2986,3060,3131,3212,3298,3381,3496,3615,3698,3764,3853,3922,3981,4076,4142,4207,4265,4330,4391,4451,4557,4618,4678,4736,4807,4926,5012,5094,5237,5312,5388,5519,5609,5687,5742,5797,5863,5932,6006,6085,6164,6237,6314,6383,6453,6550,6635,6710,6803,6896,6970,7039,7133,7185,7268,7335,7419,7503,7565,7629,7692,7762,7861,7959,8054,8148,8207,8485", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,102", "endColumns": "12,73,70,80,85,82,114,118,82,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,81,142,74,75,130,89,77,54,54,65,68,73,78,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78", "endOffsets": "314,3055,3126,3207,3293,3376,3491,3610,3693,3759,3848,3917,3976,4071,4137,4202,4260,4325,4386,4446,4552,4613,4673,4731,4802,4921,5007,5089,5232,5307,5383,5514,5604,5682,5737,5792,5858,5927,6001,6080,6159,6232,6309,6378,6448,6545,6630,6705,6798,6891,6965,7034,7128,7180,7263,7330,7414,7498,7560,7624,7687,7757,7856,7954,8049,8143,8202,8261,8559"}}]}]}