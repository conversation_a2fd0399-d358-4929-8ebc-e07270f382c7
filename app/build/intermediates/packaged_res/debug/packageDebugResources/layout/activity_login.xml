<?xml version="1.0" encoding="utf-8"?>
<android.support.design.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinatorLayout"
    android:background="@drawable/login_back"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <View
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="85"/>
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="910">
            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="836"/>
            <LinearLayout
                android:orientation="horizontal"
                android:background="@drawable/login_edit"
                android:paddingLeft="@dimen/_15dp"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="151">
                <ImageView
                    android:layout_gravity="center_vertical"
                    android:background="@drawable/login_mark_email"
                    android:layout_width="@dimen/_25dp"
                    android:layout_height="@dimen/_25dp"
                    android:adjustViewBounds="true"/>
                <EditText
                    android:id="@+id/edtLogID"
                    android:text="test1"
                    android:maxLength="20"
                    style="@style/login_edit"/>
            </LinearLayout>
            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="37"/>
            <LinearLayout
                android:orientation="horizontal"
                android:background="@drawable/login_edit"
                android:paddingLeft="@dimen/_15dp"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="151">
                <ImageView
                    android:layout_gravity="center_vertical"
                    android:background="@drawable/login_mark_pwd"
                    android:layout_width="@dimen/_25dp"
                    android:layout_height="@dimen/_25dp"
                    android:adjustViewBounds="true"/>
                <EditText
                    android:id="@+id/edtLogPass"
                    android:text="1234"
                    android:maxLength="30"
                    android:inputType="textPassword"
                    style="@style/login_edit"/>
            </LinearLayout>
            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="37"/>
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="151">
                <Button
                    android:textSize="@dimen/_18sp"
                    android:textColor="@color/white"
                    android:gravity="center_vertical"
                    android:id="@+id/btnLogin"
                    android:background="@drawable/round_button"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/user_login"
                    android:onClick="onClickBtnLogin"
                    android:textAlignment="center"/>
            </FrameLayout>
            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="558"/>
        </LinearLayout>
        <View
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="85"/>
    </LinearLayout>
</android.support.design.widget.CoordinatorLayout>
