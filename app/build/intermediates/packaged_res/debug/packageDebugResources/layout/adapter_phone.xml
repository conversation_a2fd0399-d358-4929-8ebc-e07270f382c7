<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="@dimen/_10dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageView
            android:layout_gravity="center"
            android:id="@+id/imgAction"
            android:layout_width="@dimen/_30dp"
            android:layout_height="@dimen/_30dp"
            android:src="@drawable/icon_accept"/>
        <LinearLayout
            android:orientation="vertical"
            android:paddingLeft="@dimen/_10dp"
            android:paddingTop="@dimen/_5dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="100">
            <TextView
                android:textSize="@dimen/_14sp"
                android:textColor="@color/normal_text_color5"
                android:id="@+id/txtCompany"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="업체명"
                android:maxLines="1"
                android:layout_weight="1"/>
            <TextView
                android:textSize="@dimen/_16sp"
                android:textColor="@color/white"
                android:id="@+id/txtContact"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="메모"
                android:maxLines="1"
                android:layout_weight="1"/>
        </LinearLayout>
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="match_parent">
            <TextView
                android:textSize="@dimen/_13sp"
                android:textColor="@color/normal_text_color5"
                android:gravity="bottom|right"
                android:id="@+id/txtUpdateDate"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:text="날짜"
                android:maxLines="1"
                android:layout_weight="1"/>
            <TextView
                android:textSize="@dimen/_13sp"
                android:textColor="@color/normal_text_color5"
                android:gravity="top|right"
                android:id="@+id/txtUpdateTime"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:text="날짜"
                android:maxLines="1"
                android:layout_weight="1"/>
        </LinearLayout>
    </LinearLayout>
    <View
        android:background="@color/line_color1"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"/>
</LinearLayout>
