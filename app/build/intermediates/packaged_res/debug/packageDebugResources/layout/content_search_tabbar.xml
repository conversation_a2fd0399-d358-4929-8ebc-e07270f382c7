<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    android:background="#303650"
    android:padding="@dimen/_5dp"
    android:layout_width="match_parent"
    android:layout_height="@dimen/_70dp">
    <FrameLayout
        android:id="@+id/makeCallBT"
        android:background="@drawable/social_button"
        android:padding="@dimen/_5dp"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:src="@mipmap/social_call"
            android:scaleType="centerInside"
            android:adjustViewBounds="true"/>
    </FrameLayout>
    <FrameLayout
        android:id="@+id/sendSMSBT"
        android:background="@drawable/social_button"
        android:padding="@dimen/_5dp"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:src="@mipmap/social_sms"
            android:scaleType="centerInside"
            android:adjustViewBounds="true"/>
    </FrameLayout>
    <FrameLayout
        android:id="@+id/googleBT"
        android:background="@drawable/social_button"
        android:padding="@dimen/_5dp"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:src="@mipmap/social_google"
            android:scaleType="centerInside"
            android:adjustViewBounds="true"/>
    </FrameLayout>
    <FrameLayout
        android:id="@+id/facebookBT"
        android:background="@drawable/social_button"
        android:padding="@dimen/_5dp"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:src="@mipmap/social_facebook"
            android:scaleType="centerInside"
            android:adjustViewBounds="true"/>
    </FrameLayout>
    <FrameLayout
        android:id="@+id/talkBT"
        android:background="@drawable/social_button"
        android:padding="@dimen/_5dp"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:src="@mipmap/social_kakaotalk"
            android:scaleType="centerInside"
            android:adjustViewBounds="true"/>
    </FrameLayout>
    <FrameLayout
        android:id="@+id/lineBT"
        android:background="@drawable/social_button"
        android:padding="@dimen/_5dp"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:src="@mipmap/social_line"
            android:scaleType="centerInside"
            android:adjustViewBounds="true"/>
    </FrameLayout>
</LinearLayout>
