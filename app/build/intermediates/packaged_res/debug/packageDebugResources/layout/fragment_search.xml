<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:id="@+id/searchFrame"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <include layout="@layout/content_search_tabbar"/>
    <LinearLayout
        android:orientation="vertical"
        android:background="@drawable/search_back"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="100">
        <LinearLayout
            android:orientation="horizontal"
            android:background="@drawable/shape_back_lightblue_stroke"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_50dp">
            <TextView
                android:textSize="@dimen/_16sp"
                android:textColor="@color/color_sync0"
                android:gravity="center_vertical"
                android:id="@+id/searchresultTX"
                android:paddingLeft="@dimen/_5dp"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:text="전화번호:010-1234-5678"
                android:layout_weight="100"/>
            <TextView
                android:textSize="@dimen/_16sp"
                android:textColor="@color/normal_text_color"
                android:gravity="center_vertical"
                android:id="@+id/searchResultCount"
                android:paddingRight="@dimen/_10dp"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="총 0건"/>
        </LinearLayout>
        <FrameLayout
            android:id="@+id/layoutWatermark"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="100">
            <ListView
                android:id="@+id/searchResultLV"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:divider="@null"/>
            <TextView
                android:textSize="@dimen/_28sp"
                android:textColor="@color/white"
                android:gravity="center_vertical"
                android:id="@+id/searchResultEmptyTV"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="@string/empty_data"
                android:textAlignment="center"/>
        </FrameLayout>
    </LinearLayout>
</LinearLayout>
