<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:theme="@style/ThemeOverlay.AppCompat.Dark"
    android:gravity="bottom"
    android:orientation="vertical"
    android:id="@+id/nav_header"
    android:background="@drawable/img_back2"
    android:layout_width="match_parent"
    android:layout_height="@dimen/nav_header_height">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="10"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="10">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/icon2"
            android:scaleType="fitStart"
            android:layout_marginStart="@dimen/_20dp"/>
    </LinearLayout>
    <LinearLayout
        android:gravity="center"
        android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_weight="10">
        <ImageView
            android:padding="@dimen/_5dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/telegram_icon"
            android:scaleType="fitStart"
            android:layout_marginStart="@dimen/_20dp"/>
        <TextView
            android:textSize="@dimen/_20dp"
            android:textColor="@color/white"
            android:id="@+id/textView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="faker114"
            android:layout_weight="2"/>
    </LinearLayout>
    <LinearLayout
        android:orientation="vertical"
        android:background="@color/line_color"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="5">
        <LinearLayout
            android:gravity="center_vertical"
            android:layout_gravity="center_vertical"
            android:orientation="vertical"
            android:id="@+id/linear_manryo"
            android:background="@color/SubTitleBack"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="0.1mm"
            android:layout_marginBottom="0.1mm"
            android:divider="@color/line_color"
            android:dividerHeight="@dimen/_1dp"
            android:layout_weight="16">
            <TextView
                android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                android:textSize="@dimen/_18sp"
                android:textColor="@color/color_sync0"
                android:gravity="center_vertical"
                android:id="@+id/txtCompany"
                android:paddingLeft="@dimen/_20dp"
                android:paddingRight="@dimen/_5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="업체명"
                android:layout_weight="1"/>
            <TextView
                android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                android:textSize="@dimen/_18sp"
                android:textColor="@color/color_sync0"
                android:gravity="center_vertical"
                android:id="@+id/txtRemainDay"
                android:paddingLeft="@dimen/_20dp"
                android:paddingRight="@dimen/_5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="D-0"
                android:layout_weight="1"/>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
