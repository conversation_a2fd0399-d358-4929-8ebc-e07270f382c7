<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center"
    android:orientation="vertical"
    android:id="@+id/contanerlayout"
    android:background="@color/white_alpha_color"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:padding="@dimen/_10dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1">
            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextView
                    android:textSize="@dimen/_20sp"
                    android:textColor="@color/color_sync0"
                    android:id="@+id/txt_phone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="01033446666"/>
                <TextView
                    android:textSize="@dimen/_20sp"
                    android:textColor="@color/normal_text_color2"
                    android:id="@+id/txt_phone2"
                    android:visibility="gone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="7788"/>
            </LinearLayout>
            <TextView
                android:textSize="@dimen/_12sp"
                android:textColor="@color/normal_text_color2"
                android:id="@+id/txtDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2020-11-29 12:40:45"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:textColor="@color/normal_text_color2"
                android:id="@+id/txtBlockComment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="모르는 번호 차단"/>
        </LinearLayout>
    </LinearLayout>
    <View
        android:background="@color/line_color1"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"/>
</LinearLayout>
