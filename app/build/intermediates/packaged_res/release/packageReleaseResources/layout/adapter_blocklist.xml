<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center"
    android:orientation="vertical"
    android:id="@+id/contanerlayout"
    android:background="@color/white_alpha_color"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:padding="@dimen/_10dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1">
            <TextView
                android:textSize="@dimen/_20sp"
                android:textColor="@color/color_sync0"
                android:id="@+id/txt_phone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="XXXXXXXXXXX"/>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:textSize="@dimen/_12sp"
                    android:textColor="@color/normal_text_color2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="오늘 블럭횟수  "/>
                <TextView
                    android:textSize="@dimen/_12sp"
                    android:textColor="@color/normal_text_color2"
                    android:id="@+id/txt_blockcount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"/>
            </LinearLayout>
        </LinearLayout>
        <RelativeLayout
            android:gravity="center"
            android:id="@+id/btnDelete"
            android:paddingLeft="@dimen/_15dp"
            android:paddingRight="@dimen/_15dp"
            android:layout_width="wrap_content"
            android:layout_height="match_parent">
            <ImageView
                android:layout_gravity="center"
                android:layout_width="@dimen/_25dp"
                android:layout_height="@dimen/_25dp"
                android:src="@drawable/icon_close"/>
        </RelativeLayout>
    </LinearLayout>
    <View
        android:background="@color/line_color1"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_1dp"/>
</LinearLayout>
