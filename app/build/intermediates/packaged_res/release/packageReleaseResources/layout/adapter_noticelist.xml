<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:paddingTop="@dimen/_5dp"
    android:paddingBottom="@dimen/_5dp"
    android:layout_width="match_parent"
    android:layout_height="@dimen/_150dp"
    android:paddingStart="@dimen/_10dp"
    android:paddingEnd="@dimen/_10dp">
    <LinearLayout
        android:orientation="vertical"
        android:background="@drawable/default_board"
        android:padding="@dimen/_10dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:textSize="@dimen/_18sp"
            android:textColor="@color/normal_text_color"
            android:id="@+id/txtNoticeTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_10dp"
            android:text="안녕하세요"/>
        <TextView
            android:textSize="@dimen/_12sp"
            android:textColor="@color/normal_text_color2"
            android:id="@+id/txtNoticeDate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/_10dp"
            android:layout_marginRight="@dimen/_10dp"
            android:text="2020-09-28 오후 03:08:24"/>
        <View
            android:id="@+id/divider2"
            android:background="?android:attr/listDivider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="@dimen/_10dp"
            android:layout_marginBottom="@dimen/_10dp"/>
        <TextView
            android:textSize="@dimen/_14sp"
            android:textColor="@color/normal_text_color"
            android:id="@+id/txtNoticeContent"
            android:scrollbars="none"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:text="테스트입니다. "
            android:layout_weight="100"
            android:layout_marginStart="@dimen/_10dp"
            android:layout_marginEnd="@dimen/_10dp"/>
    </LinearLayout>
</LinearLayout>
