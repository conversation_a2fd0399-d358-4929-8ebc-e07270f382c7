<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:divider="@color/line_color1"
    android:dividerHeight="1dp">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/item"
            android:padding="@dimen/_8dp"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">
            <TextView
                android:textSize="@dimen/_16sp"
                android:textColor="@color/normal_text_color"
                android:gravity="top"
                android:id="@+id/txtMemo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_10dp"
                android:text="모바일"/>
            <TextView
                android:textSize="@dimen/_16sp"
                android:textColor="@color/color_sync0"
                android:id="@+id/txtPhoneNumber"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_10dp"
                android:text="010-5209-559"/>
            <TextView
                android:textSize="@dimen/_12sp"
                android:textColor="@color/normal_text_color3"
                android:id="@+id/txtDate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/_10dp"
                android:text="date"/>
        </LinearLayout>
        <RelativeLayout
            android:gravity="center"
            android:id="@+id/btnCall"
            android:background="@drawable/mini_call_button"
            android:paddingLeft="@dimen/_15dp"
            android:paddingRight="@dimen/_15dp"
            android:layout_width="wrap_content"
            android:layout_height="match_parent">
            <ImageView
                android:layout_gravity="center"
                android:id="@+id/imgPhone"
                android:layout_width="@dimen/_30dp"
                android:layout_height="@dimen/_30dp"
                android:src="@mipmap/call"/>
        </RelativeLayout>
    </LinearLayout>
    <View
        android:id="@+id/divider5"
        android:background="@color/line_color1"
        android:layout_width="match_parent"
        android:layout_height="1dp"/>
</LinearLayout>
