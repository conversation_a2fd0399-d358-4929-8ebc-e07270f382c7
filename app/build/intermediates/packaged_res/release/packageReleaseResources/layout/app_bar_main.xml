<?xml version="1.0" encoding="utf-8"?>
<android.support.design.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <android.support.design.widget.AppBarLayout
        android:theme="@style/AppTheme.AppBarOverlay"
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <android.support.v7.widget.Toolbar
            android:layout_gravity="center_horizontal"
            android:id="@+id/toolbar"
            android:background="@color/back_color1"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:title=""
            android:alpha="64"
            app:layout_anchor="@+id/appBarLayout"
            app:layout_anchorGravity="center"
            app:theme="@style/ToolbarColoredBackArrow">
            <LinearLayout
                android:gravity="center"
                android:layout_gravity="center"
                android:orientation="horizontal"
                android:layout_width="wrap_content"
                android:layout_height="match_parent">
                <TextView
                    android:id="@+id/titleTV"
                    android:text="---"
                    style="@style/toolbarWhiteText"/>
            </LinearLayout>
        </android.support.v7.widget.Toolbar>
    </android.support.design.widget.AppBarLayout>
    <include layout="@layout/content_main"/>
</android.support.design.widget.CoordinatorLayout>
