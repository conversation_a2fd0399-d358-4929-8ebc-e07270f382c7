<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/back_color2"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:orientation="horizontal"
        android:background="@drawable/default_editbox"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_50dp"
        android:layout_marginLeft="@dimen/_20dp"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginRight="@dimen/_20dp"
        android:layout_marginBottom="@dimen/_10dp">
        <ImageView
            android:layout_gravity="center_vertical"
            android:background="@drawable/icon_search_mark"
            android:visibility="visible"
            android:layout_width="@dimen/_25dp"
            android:layout_height="@dimen/_25dp"
            android:layout_marginLeft="@dimen/_15dp"
            android:layout_marginStart="@dimen/_15dp"/>
        <EditText
            android:textSize="@dimen/_16sp"
            android:textColor="@color/normal_text_color4"
            android:textColorHint="@color/normal_text_color3"
            android:layout_gravity="center_vertical"
            android:id="@+id/edtSearch"
            android:background="@null"
            android:paddingLeft="@dimen/_5dp"
            android:visibility="visible"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:hint="@string/search_hint"
            android:maxLength="20"
            android:layout_weight="100"
            android:inputType="phone"
            android:imeOptions="actionSearch"/>
        <Button
            android:textSize="@dimen/_16sp"
            android:textColor="#ffffff"
            android:layout_gravity="center_vertical"
            android:id="@+id/btnsearch"
            android:background="@drawable/search_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_4dp"
            android:layout_marginLeft="@dimen/_10dp"
            android:text="조회"
            android:layout_marginStart="@dimen/_10dp"/>
    </LinearLayout>
</FrameLayout>
