<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:id="@+id/mainFragment"
    android:background="#ececec"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <include layout="@layout/content_main_search"/>
        <LinearLayout
            android:orientation="horizontal"
            android:background="@drawable/shape_back_lightblue_stroke"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_50dp">
            <TextView
                android:textSize="@dimen/_16sp"
                android:textColor="@color/normal_text_color"
                android:gravity="center_vertical"
                android:paddingLeft="@dimen/_10dp"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:text=" 최근에 온 전화 및 SMS"
                android:layout_weight="100"/>
            <TextView
                android:textSize="@dimen/_16sp"
                android:textColor="@color/normal_text_color"
                android:gravity="center_vertical"
                android:id="@+id/recentCallTV"
                android:paddingRight="@dimen/_10dp"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="총 0건"/>
            <Button
                android:textColor="@color/white"
                android:id="@+id/btnRefresh"
                android:background="@drawable/course_button_style"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="새로고침"/>
        </LinearLayout>
        <FrameLayout
            android:orientation="horizontal"
            android:id="@+id/layoutWatermark"
            android:background="@drawable/search_back"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <ListView
                android:id="@+id/lstRecentCall"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
            <TextView
                android:textSize="@dimen/_28sp"
                android:textColor="@color/blue_light"
                android:gravity="center_vertical"
                android:id="@+id/incomeEmptyTV"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="@string/noCallHistory"
                android:textAlignment="center"/>
        </FrameLayout>
    </LinearLayout>
</FrameLayout>
