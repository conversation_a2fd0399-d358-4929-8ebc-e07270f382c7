<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:orientation="vertical"
        android:background="@drawable/img_back2"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:textSize="@dimen/_20sp"
            android:textColor="@color/normal_text_color"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_16dp"
            android:text="시작번호 차단설정"/>
        <TextView
            android:textSize="@dimen/_16sp"
            android:textColor="@color/normal_text_color"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="설정하신 시작번호의 모든 통화수신을\10자동으로 차단합니다."/>
        <TextView
            android:textSize="@dimen/_16sp"
            android:textColor="@color/color_sync1"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="예) 010-55 까지만 입력하시면\10010-55로 시작되는 모든 번호를\10차단합니다.\10(문자는 차단되지 않습니다.)"/>
        <TextView
            android:textSize="@dimen/_16sp"
            android:textColor="@color/normal_text_color"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="자동차단 제외 대상\10* 연락처 등록번호"/>
        <LinearLayout
            android:background="@drawable/default_editbox"
            android:padding="@dimen/_5dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_16dp">
            <EditText
                android:textSize="@dimen/_20sp"
                android:textColor="@color/color_sync0"
                android:textColorHint="@color/color_sync0"
                android:id="@+id/txtPhoneNumber"
                android:background="@color/white"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="최소 4자리 이상 입력하세요"
                android:inputType="phone"
                android:textAlignment="center"/>
        </LinearLayout>
        <TextView
            android:textSize="@dimen/_16sp"
            android:textColor="@color/color_sync1"
            android:id="@+id/txtError"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/_10dp"
            android:layout_marginStart="@dimen/_20dp"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <Button
                android:textColor="@color/white"
                android:id="@+id/btnCancel"
                android:background="@color/color_sync0"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_cancel"
                android:layout_weight="1"
                style="?android:attr/buttonBarButtonStyle"/>
            <Button
                android:textColor="@color/white"
                android:id="@+id/btnOk"
                android:background="@color/color_sync1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_ok"
                android:layout_weight="1"
                style="?android:attr/buttonBarButtonStyle"/>
        </LinearLayout>
    </LinearLayout>
    <Button
        android:layout_gravity="end|top"
        android:id="@+id/btnClose"
        android:background="@drawable/pop_close"
        android:layout_width="@dimen/_30dp"
        android:layout_height="@dimen/_30dp"
        android:layout_margin="@dimen/_5dp"/>
</FrameLayout>
