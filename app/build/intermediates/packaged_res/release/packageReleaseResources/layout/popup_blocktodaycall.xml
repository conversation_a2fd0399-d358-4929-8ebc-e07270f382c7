<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:orientation="vertical"
        android:background="@drawable/img_back2"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:textSize="@dimen/_20sp"
            android:textColor="@color/normal_text_color"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_16dp"
            android:text="오늘 전화문의 차단"/>
        <TextView
            android:textSize="@dimen/_16sp"
            android:textColor="@color/normal_text_color"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="1개의 번호가 앱을 사용하시는 업체에\101일 3~10곳 이상\10(횟수는 사용자가 직접 설정)\10전화를 했을시 해당번호는 앱에서\10자동차단 합니다.\10당일 하루 차단이고 다음날은\10자동차단 해제됩니다."/>
        <LinearLayout
            android:padding="@dimen/_16dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:textColor="@color/normal_text_color"
                android:gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="차단횟수 설정"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginEnd="@dimen/_16dp"/>
            <LinearLayout
                android:background="@drawable/default_editbox"
                android:padding="@dimen/_5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10dp"
                android:layout_marginEnd="@dimen/_16dp">
                <EditText
                    android:textSize="@dimen/_20sp"
                    android:textColor="@color/color_sync0"
                    android:id="@+id/txtBlockLimit"
                    android:background="@color/white"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:textAlignment="center"/>
            </LinearLayout>
        </LinearLayout>
        <TextView
            android:textSize="@dimen/_16sp"
            android:textColor="@color/color_sync1"
            android:id="@+id/txtError"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/_10dp"
            android:layout_marginStart="@dimen/_20dp"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <Button
                android:textColor="@color/white"
                android:id="@+id/btnCancel"
                android:background="@color/color_sync0"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_cancel"
                android:layout_weight="1"
                style="?android:attr/buttonBarButtonStyle"/>
            <Button
                android:textColor="@color/white"
                android:id="@+id/btnOk"
                android:background="@color/color_sync1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_ok"
                android:layout_weight="1"
                style="?android:attr/buttonBarButtonStyle"/>
        </LinearLayout>
    </LinearLayout>
    <Button
        android:layout_gravity="end|top"
        android:id="@+id/btnClose"
        android:background="@drawable/pop_close"
        android:layout_width="@dimen/_30dp"
        android:layout_height="@dimen/_30dp"
        android:layout_margin="@dimen/_5dp"/>
</FrameLayout>
