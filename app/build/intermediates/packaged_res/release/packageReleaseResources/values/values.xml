<?xml version="1.0" encoding="utf-8"?>
<resources>
    <array name="drawer_items">
        <item>전화번호 검색</item>
        <item>콜폭방어</item>
        <item>공지사항</item>
        <item>환경설정</item>
        <item>로그아웃</item>
    </array>
    <color name="Gray">#808080</color>
    <color name="Gray_divider">#141921</color>
    <color name="LightGray">#d0d0d0</color>
    <color name="Login_Split">#eeeeee</color>
    <color name="SubTitleBack">#1a1c26</color>
    <color name="SubTitleBackLine">#242836</color>
    <color name="back_color">#0a0d20</color>
    <color name="back_color1">#3a3f5d</color>
    <color name="back_color2">#303650</color>
    <color name="back_color3">#232637</color>
    <color name="black">#000000</color>
    <color name="blue_default">#1590fa</color>
    <color name="blue_light">#79bdfa</color>
    <color name="colorAccent">#FF4081</color>
    <color name="colorPrimary">#3F51B5</color>
    <color name="colorPrimaryDark">#303F9F</color>
    <color name="color_sync0">#8f7bdd</color>
    <color name="color_sync1">#c656a1</color>
    <color name="color_sync2">#636363</color>
    <color name="color_sync3">#e0376a</color>
    <color name="color_sync4">#f86591</color>
    <color name="color_sync5">#e03569</color>
    <color name="divider_normal_color">#e8e8e8</color>
    <color name="green_default">#4caf50</color>
    <color name="green_light">#81c784</color>
    <color name="greenlight_alpha_color">#40c0ffc0</color>
    <color name="line_color">#2c2f40</color>
    <color name="line_color1">#5c5d6b</color>
    <color name="menu_item_back_color">#2a4057</color>
    <color name="menu_item_divider_color">#5095bf</color>
    <color name="menu_item_sel_color">#65737c</color>
    <color name="normal_text_color">#ffffff</color>
    <color name="normal_text_color2">#e2e2e2</color>
    <color name="normal_text_color3">#878787</color>
    <color name="normal_text_color4">#303030</color>
    <color name="normal_text_color5">#9d9d9d</color>
    <color name="normal_text_color6">#9d9d9d</color>
    <color name="orange_default">#ff9800</color>
    <color name="orange_light">#ffb74d</color>
    <color name="red">#ff0000</color>
    <color name="red_default">#f44336</color>
    <color name="red_light">#e57373</color>
    <color name="white">#ffffff</color>
    <color name="white_alpha_color">#20ffffff</color>
    <color name="yellow_default">#ffeb3b</color>
    <color name="yellow_light">#fff176</color>
    <dimen name="_10dp">10dp</dimen>
    <dimen name="_12sp">12sp</dimen>
    <dimen name="_13sp">13sp</dimen>
    <dimen name="_14sp">14sp</dimen>
    <dimen name="_150dp">150dp</dimen>
    <dimen name="_15dp">15dp</dimen>
    <dimen name="_16dp">16dp</dimen>
    <dimen name="_16sp">16sp</dimen>
    <dimen name="_18sp">18sp</dimen>
    <dimen name="_1dp">1dp</dimen>
    <dimen name="_20dp">20dp</dimen>
    <dimen name="_20sp">20sp</dimen>
    <dimen name="_25dp">25dp</dimen>
    <dimen name="_280dp">280dp</dimen>
    <dimen name="_2dp">2dp</dimen>
    <dimen name="_30dp">30dp</dimen>
    <dimen name="_40dp">40dp</dimen>
    <dimen name="_4dp">4dp</dimen>
    <dimen name="_50dp">50dp</dimen>
    <dimen name="_5dp">5dp</dimen>
    <dimen name="_60dp">60dp</dimen>
    <dimen name="_70dp">70dp</dimen>
    <dimen name="_8dp">8dp</dimen>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="fab_margin">16dp</dimen>
    <dimen name="nav_header_height">176dp</dimen>
    <dimen name="nav_header_vertical_spacing">8dp</dimen>
    <drawable name="ic_menu_camera">@android:drawable/ic_menu_camera</drawable>
    <drawable name="ic_menu_gallery">@android:drawable/ic_menu_gallery</drawable>
    <drawable name="ic_menu_manage">@android:drawable/ic_menu_manage</drawable>
    <drawable name="ic_menu_send">@android:drawable/ic_menu_send</drawable>
    <drawable name="ic_menu_share">@android:drawable/ic_menu_share</drawable>
    <drawable name="ic_menu_slideshow">@android:drawable/ic_menu_slideshow</drawable>
    <drawable name="notification_template_icon_bg">#3333b5e5</drawable>
    <drawable name="notification_template_icon_low_bg">#0cffffff</drawable>
    <integer name="status_bar_notification_info_maxnum">999</integer>
    <string name="abc_action_bar_home_description">Navigate home</string>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_activity_chooser_view_see_all">See all</string>
    <string name="abc_activitychooserview_choose_application">Choose an app</string>
    <string name="abc_capital_off">OFF</string>
    <string name="abc_capital_on">ON</string>
    <string name="abc_font_family_body_1_material">sans-serif</string>
    <string name="abc_font_family_body_2_material">sans-serif-medium</string>
    <string name="abc_font_family_button_material">sans-serif-medium</string>
    <string name="abc_font_family_caption_material">sans-serif</string>
    <string name="abc_font_family_display_1_material">sans-serif</string>
    <string name="abc_font_family_display_2_material">sans-serif</string>
    <string name="abc_font_family_display_3_material">sans-serif</string>
    <string name="abc_font_family_display_4_material">sans-serif-light</string>
    <string name="abc_font_family_headline_material">sans-serif</string>
    <string name="abc_font_family_menu_material">sans-serif</string>
    <string name="abc_font_family_subhead_material">sans-serif</string>
    <string name="abc_font_family_title_material">sans-serif-medium</string>
    <string name="abc_menu_alt_shortcut_label">Alt+</string>
    <string name="abc_menu_ctrl_shortcut_label">Ctrl+</string>
    <string name="abc_menu_delete_shortcut_label">delete</string>
    <string name="abc_menu_enter_shortcut_label">enter</string>
    <string name="abc_menu_function_shortcut_label">Function+</string>
    <string name="abc_menu_meta_shortcut_label">Meta+</string>
    <string name="abc_menu_shift_shortcut_label">Shift+</string>
    <string name="abc_menu_space_shortcut_label">space</string>
    <string name="abc_menu_sym_shortcut_label">Sym+</string>
    <string name="abc_prepend_shortcut_label">Menu+</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_query">Search query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_shareactionprovider_share_with">Share with</string>
    <string name="abc_shareactionprovider_share_with_application">Share with %s</string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="action_settings">Settings</string>
    <string name="app_name">FAKER</string>
    <string name="appbar_scrolling_view_behavior">android.support.design.widget.AppBarLayout$ScrollingViewBehavior</string>
    <string name="blacklist_request_permissions">Request permissions</string>
    <string name="bottom_sheet_behavior">android.support.design.widget.BottomSheetBehavior</string>
    <string name="cancel">취소</string>
    <string name="character_counter_content_description">Character limit exceeded %1$d of %2$d</string>
    <string name="character_counter_pattern">%1$d / %2$d</string>
    <string name="empty_data">등록된 정보없음</string>
    <string name="fab_transformation_scrim_behavior">android.support.design.transformation.FabTransformationScrimBehavior</string>
    <string name="fab_transformation_sheet_behavior">android.support.design.transformation.FabTransformationSheetBehavior</string>
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="hide_bottom_view_on_scroll_behavior">android.support.design.behavior.HideBottomViewOnScrollBehavior</string>
    <string name="mtrl_chip_close_icon_content_description">Remove %1$s</string>
    <string name="navigation_drawer_close">Close navigation drawer</string>
    <string name="navigation_drawer_open">Open navigation drawer</string>
    <string name="noCallHistory">콜/SMS이력 없음</string>
    <string name="noData">-</string>
    <string name="password_toggle_content_description">Show password</string>
    <string name="path_password_eye">M12,4.5C7,4.5 2.73,7.61 1,12c1.73,4.39 6,7.5 11,7.5s9.27,-3.11 11,-7.5c-1.73,-4.39 -6,-7.5 -11,-7.5zM12,17c-2.76,0 -5,-2.24 -5,-5s2.24,-5 5,-5 5,2.24 5,5 -2.24,5 -5,5zM12,9c-1.66,0 -3,1.34 -3,3s1.34,3 3,3 3,-1.34 3,-3 -1.34,-3 -3,-3z</string>
    <string name="path_password_eye_mask_strike_through">M2,4.27 L19.73,22 L22.27,19.46 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_eye_mask_visible">M2,4.27 L2,4.27 L4.54,1.73 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_strike_through">M3.27,4.27 L19.74,20.74</string>
    <string name="permissions_required">퍼미션을 모두 허용해주셔야 이 앱을 이용하실수 있습니다.</string>
    <string name="phonesearch_item">전화번호 검색</string>
    <string name="popup_position">팝업창 위치</string>
    <string name="question_item">문의사항</string>
    <string name="report_item">공지사항</string>
    <string name="search_hint">전화번호를 입력하세요</string>
    <string name="search_menu_title">Search</string>
    <string name="service_fail">데이터 신호 약함</string>
    <string name="setting">설정</string>
    <string name="setting_IPlabel">IP주소</string>
    <string name="setting_Portlabel">Port주소</string>
    <string name="setting_title">서버설정</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="tab_txt_name">FAKER</string>
    <string name="tab_txt_number">전화번호: 010-2344-4517</string>
    <string name="text_cancel">취소하기</string>
    <string name="text_ok">확인</string>
    <string name="title_phoneNumber">전화번호검색</string>
    <string name="today_call_request">오늘 전화문의 보기</string>
    <string name="user_id_hint">아이디</string>
    <string name="user_login">로그인</string>
    <string name="user_login_title">로그인정보</string>
    <string name="user_pass_hint">패스워드</string>
    <string name="wait">잠시만 기다려주세요...</string>
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>
    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="AppTheme.NoActionBar" parent="AppTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/>
    <style name="ToolbarColoredBackArrow" parent="AppTheme">
        <item name="android:textColorSecondary">@color/white</item>
    </style>
    <style name="login_edit">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/login_edit</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textColorHint">@color/normal_text_color3</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
    </style>
    <style name="toolbarWhiteText" parent="AppTheme">
        <item name="android:textColor">@color/white</item>
        <item name="android:textColorPrimary">@color/white</item>
    </style>
</resources>