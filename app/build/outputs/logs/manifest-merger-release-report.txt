-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:2:1-87:12
INJECTED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:2:1-87:12
INJECTED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:2:1-87:12
INJECTED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:2:1-87:12
MERGED from [androidx.navigation:navigation-common:2.7.5] /Users/<USER>/.gradle/caches/8.13/transforms/c1b89fc28ae63c126d6bc14c3d32ff5c/transformed/navigation-common-2.7.5/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] /Users/<USER>/.gradle/caches/8.13/transforms/106842de86cebc2f8ebf6dcf40b64449/transformed/navigation-runtime-2.7.5/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.7.5] /Users/<USER>/.gradle/caches/8.13/transforms/db66c703f5eb48fb227ed863391f1fb8/transformed/navigation-fragment-2.7.5/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui:2.7.5] /Users/<USER>/.gradle/caches/8.13/transforms/32abaf143a115377682b07bc710faabb/transformed/navigation-ui-2.7.5/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/b3a61cce2ad1dad98c4e5ab92fec4e8c/transformed/material-1.10.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/873971c02893ba5813f2b8448e796a0c/transformed/constraintlayout-2.1.4/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/7f66a4a258f636fb84431e3b2ad87d2f/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/9f4ccd3212b97d78ca5085eb75c4d8ac/transformed/appcompat-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/da37d132185ed27c1c75821ab23fb60c/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/0410608ffaa342aac125d20f1650f9cd/transformed/jetified-fragment-ktx-1.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/80ca22431b7579dc933b9e324f95bbe7/transformed/fragment-1.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/145f66b27eed9468282ccf8a29ad1fed/transformed/jetified-activity-ktx-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/5503a297cf0232b1c1b7e7a087728457/transformed/jetified-activity-1.8.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/0ebdb6c5cd04141622077eb48ba32e82/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/7d4c1130c1a3f20467892e9db1658fa0/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/1b4c87c46fb9550103efed6aa764f445/transformed/drawerlayout-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/a821ffa709e020f953e0c9f322106aba/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/a3108c1c59b46736c895ab3c89d5aae6/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/f32668481dc95265ab4455169acf6ba4/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/aeb3e8ee42c2af94b9712e70c8a9bd82/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d4faa566e8a6771382dd38f32caaa3ca/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/3057c1fd72d1645568d70dd96f90106b/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f9a895950b616dfa7e5aa7af0114f835/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/ef33e05e5133a112c6cc8797453b8a17/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/e2f488867473931bacf7d74ffb7e0cab/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f28e7fb2cbc8affc0e7304e4bbec3b58/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/8489119e8edda9fcf25ed70f386e2843/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/2897dc54a7dcdab0c19a4ebda1ffa882/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b0bd24ec7b0d770a64d28fe3fe12e5f5/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/8297f2dabb9719edd1659d08fb27cc10/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/39852eee3ae3c98d347fb7baf2c7615b/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fc7aadd4f40d4faffc6421d977a5efdf/transformed/jetified-lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/03cef59ae30cc738d9ad9e90e4e6a297/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/390511780a58212b9bcc53353a9a67f4/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/f4641ea73228121ffda497d7f917005e/transformed/jetified-lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/f944fd1f7ee44eb54123d84f12dcd93b/transformed/jetified-lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/2d219c53b990020673e504ed09e916ad/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/386436eb15f2a6ed3e0b42743092dc3a/transformed/jetified-core-ktx-1.9.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.13/transforms/dcd58c4311b67921681d561f6c8e99f9/transformed/transition-1.4.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/09a28d2de6989074da233c15dd999f2e/transformed/jetified-window-1.0.0/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/4d8a1eff85be8cf5617f063d344bac8a/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/fb32ea96f6e2e5e5367b8c85544cc4d0/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/86b97bf4e36882146a24065f7a7b26b9/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/9fd03fa9ef81c27f51a7215de25f49d1/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/3c49456e0a380355fa746a924056ccf9/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/99b6a52dcee8cf4582415a4f71502ea0/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/8466d4ef212ac1c6dc08c0b6f9141437/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/974558053e172ebbdc10993b111d52e8/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/3110a38ff0bd5ea4f95a0f5b01160d70/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/8ca61c5c50caec4fb1c9c9a3eeb803d2/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/4dcb73d6529b6faa07265bbbd72c20da/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:17:1-22:12
	package
		INJECTED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:2:11-69
uses-permission#android.permission.READ_LOGS
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:4:5-67
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:4:22-65
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:5:5-80
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:5:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:6:5-79
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:6:22-77
uses-permission#android.permission.PROCESS_OUTGOING_CALLS
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:7:5-80
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:7:22-78
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:8:5-66
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:8:22-64
uses-permission#android.permission.READ_CONTACTS
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:9:5-71
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:9:22-69
uses-permission#android.permission.WRITE_CONTACTS
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:10:5-72
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:10:22-70
uses-permission#android.permission.RECEIVE_SMS
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:11:5-69
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:11:22-67
uses-permission#android.permission.READ_SMS
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:12:5-66
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:12:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:13:5-78
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:13:22-76
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:14:5-77
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:14:22-75
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:15:5-80
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:15:22-78
uses-permission#android.permission.ANSWER_PHONE_CALLS
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:16:5-76
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:16:22-74
uses-permission#android.permission.CALL_PHONE
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:17:5-68
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:17:22-66
uses-permission#android.permission.MODIFY_PHONE_STATE
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:18:5-76
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:18:22-74
uses-permission#android.permission.READ_CALL_LOG
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:19:5-71
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:19:22-69
uses-permission#android.permission.WRITE_CALL_LOG
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:20:5-72
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:20:22-70
uses-permission#android.permission.READ_PHONE_STATE
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:21:5-74
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:21:22-72
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:22:5-76
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:22:22-74
application
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:23:5-86:19
INJECTED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:23:5-86:19
MERGED from [com.google.android.material:material:1.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/b3a61cce2ad1dad98c4e5ab92fec4e8c/transformed/material-1.10.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/b3a61cce2ad1dad98c4e5ab92fec4e8c/transformed/material-1.10.0/AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/873971c02893ba5813f2b8448e796a0c/transformed/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/873971c02893ba5813f2b8448e796a0c/transformed/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/7d4c1130c1a3f20467892e9db1658fa0/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/7d4c1130c1a3f20467892e9db1658fa0/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/39852eee3ae3c98d347fb7baf2c7615b/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/39852eee3ae3c98d347fb7baf2c7615b/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/09a28d2de6989074da233c15dd999f2e/transformed/jetified-window-1.0.0/AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/09a28d2de6989074da233c15dd999f2e/transformed/jetified-window-1.0.0/AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/86b97bf4e36882146a24065f7a7b26b9/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/86b97bf4e36882146a24065f7a7b26b9/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/9fd03fa9ef81c27f51a7215de25f49d1/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/9fd03fa9ef81c27f51a7215de25f49d1/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml
	android:supportsRtl
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:28:9-35
	android:label
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:25:9-41
	android:appComponentFactory
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:30:9-77
		REJECTED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/AndroidManifest.xml:28:18-86
	android:roundIcon
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:29:9-41
	android:icon
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:26:9-36
	android:allowBackup
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:27:9-35
	android:theme
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:24:9-40
	tools:replace
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:31:9-52
activity#com.developer.faker.Activity.LoginActivity
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:32:9-42:20
	android:screenOrientation
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:35:13-49
	android:exported
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:36:13-36
	android:theme
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:33:13-56
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:34:13-70
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.DEFAULT+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:37:13-41:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:38:17-68
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:38:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:39:17-76
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:39:27-74
category#android.intent.category.DEFAULT
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:40:17-75
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:40:27-73
activity#com.developer.faker.Activity.MainActivity
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:43:9-47:39
	android:screenOrientation
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:46:13-49
	android:exported
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:47:13-37
	android:theme
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:44:13-56
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:45:13-69
service#com.developer.faker.Service.MainService
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:48:9-56:19
	android:enabled
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:51:13-35
	android:exported
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:52:13-37
	android:permission
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:50:13-72
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:49:13-67
intent-filter#action:name:com.developer.faker.Service.MainService
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:53:13-55:29
action#com.developer.faker.Service.MainService
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:54:17-81
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:54:25-79
receiver#com.developer.faker.Service.RestartReceiver
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:57:9-104
	android:exported
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:57:78-102
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:57:19-77
receiver#com.developer.faker.Service.BootReceiver
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:58:9-62:20
	android:exported
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:58:75-98
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:58:19-74
intent-filter#action:name:android.intent.action.BOOT_COMPLETED
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:59:13-61:29
action#android.intent.action.BOOT_COMPLETED
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:60:17-78
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:60:25-76
service#com.developer.faker.Service.FloatingViewService
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:63:9-66:39
	android:enabled
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:65:13-35
	android:exported
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:66:13-37
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:64:13-75
receiver#com.developer.faker.Service.NewPhonecallReceiver
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:67:9-76:20
	android:enabled
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:69:13-35
	android:exported
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:70:13-36
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:68:13-76
intent-filter#action:name:android.intent.action.NEW_OUTGOING_CALL+action:name:android.intent.action.PHONE_STATE+action:name:android.provider.Telephony.SMS_RECEIVED
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:71:13-75:29
	android:priority
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:71:28-57
action#android.intent.action.PHONE_STATE
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:72:17-75
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:72:25-73
action#android.intent.action.NEW_OUTGOING_CALL
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:73:17-81
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:73:25-79
action#android.provider.Telephony.SMS_RECEIVED
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:74:17-81
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:74:25-79
provider#androidx.core.content.FileProvider
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:77:9-85:20
	android:grantUriPermissions
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:81:13-47
	android:authorities
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:80:13-54
	android:exported
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:79:13-37
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:78:13-62
meta-data#androidx.core.FILE_PROVIDER_PATHS
ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:82:13-84:57
	android:resource
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:84:17-55
	android:name
		ADDED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml:83:17-65
uses-sdk
INJECTED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml
MERGED from [androidx.navigation:navigation-common:2.7.5] /Users/<USER>/.gradle/caches/8.13/transforms/c1b89fc28ae63c126d6bc14c3d32ff5c/transformed/navigation-common-2.7.5/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] /Users/<USER>/.gradle/caches/8.13/transforms/c1b89fc28ae63c126d6bc14c3d32ff5c/transformed/navigation-common-2.7.5/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] /Users/<USER>/.gradle/caches/8.13/transforms/106842de86cebc2f8ebf6dcf40b64449/transformed/navigation-runtime-2.7.5/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] /Users/<USER>/.gradle/caches/8.13/transforms/106842de86cebc2f8ebf6dcf40b64449/transformed/navigation-runtime-2.7.5/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] /Users/<USER>/.gradle/caches/8.13/transforms/db66c703f5eb48fb227ed863391f1fb8/transformed/navigation-fragment-2.7.5/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] /Users/<USER>/.gradle/caches/8.13/transforms/db66c703f5eb48fb227ed863391f1fb8/transformed/navigation-fragment-2.7.5/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] /Users/<USER>/.gradle/caches/8.13/transforms/32abaf143a115377682b07bc710faabb/transformed/navigation-ui-2.7.5/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] /Users/<USER>/.gradle/caches/8.13/transforms/32abaf143a115377682b07bc710faabb/transformed/navigation-ui-2.7.5/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/b3a61cce2ad1dad98c4e5ab92fec4e8c/transformed/material-1.10.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/b3a61cce2ad1dad98c4e5ab92fec4e8c/transformed/material-1.10.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/873971c02893ba5813f2b8448e796a0c/transformed/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/873971c02893ba5813f2b8448e796a0c/transformed/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/7f66a4a258f636fb84431e3b2ad87d2f/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/7f66a4a258f636fb84431e3b2ad87d2f/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/9f4ccd3212b97d78ca5085eb75c4d8ac/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/9f4ccd3212b97d78ca5085eb75c4d8ac/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/da37d132185ed27c1c75821ab23fb60c/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/da37d132185ed27c1c75821ab23fb60c/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/0410608ffaa342aac125d20f1650f9cd/transformed/jetified-fragment-ktx-1.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/0410608ffaa342aac125d20f1650f9cd/transformed/jetified-fragment-ktx-1.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/80ca22431b7579dc933b9e324f95bbe7/transformed/fragment-1.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/80ca22431b7579dc933b9e324f95bbe7/transformed/fragment-1.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/145f66b27eed9468282ccf8a29ad1fed/transformed/jetified-activity-ktx-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/145f66b27eed9468282ccf8a29ad1fed/transformed/jetified-activity-ktx-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/5503a297cf0232b1c1b7e7a087728457/transformed/jetified-activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/5503a297cf0232b1c1b7e7a087728457/transformed/jetified-activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/0ebdb6c5cd04141622077eb48ba32e82/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/0ebdb6c5cd04141622077eb48ba32e82/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/7d4c1130c1a3f20467892e9db1658fa0/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/7d4c1130c1a3f20467892e9db1658fa0/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/1b4c87c46fb9550103efed6aa764f445/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/1b4c87c46fb9550103efed6aa764f445/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/a821ffa709e020f953e0c9f322106aba/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/a821ffa709e020f953e0c9f322106aba/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/a3108c1c59b46736c895ab3c89d5aae6/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/a3108c1c59b46736c895ab3c89d5aae6/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/f32668481dc95265ab4455169acf6ba4/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/f32668481dc95265ab4455169acf6ba4/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/aeb3e8ee42c2af94b9712e70c8a9bd82/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/aeb3e8ee42c2af94b9712e70c8a9bd82/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d4faa566e8a6771382dd38f32caaa3ca/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/d4faa566e8a6771382dd38f32caaa3ca/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/3057c1fd72d1645568d70dd96f90106b/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/3057c1fd72d1645568d70dd96f90106b/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f9a895950b616dfa7e5aa7af0114f835/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f9a895950b616dfa7e5aa7af0114f835/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/ef33e05e5133a112c6cc8797453b8a17/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/ef33e05e5133a112c6cc8797453b8a17/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/e2f488867473931bacf7d74ffb7e0cab/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/e2f488867473931bacf7d74ffb7e0cab/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f28e7fb2cbc8affc0e7304e4bbec3b58/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f28e7fb2cbc8affc0e7304e4bbec3b58/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/8489119e8edda9fcf25ed70f386e2843/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/8489119e8edda9fcf25ed70f386e2843/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/2897dc54a7dcdab0c19a4ebda1ffa882/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/2897dc54a7dcdab0c19a4ebda1ffa882/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b0bd24ec7b0d770a64d28fe3fe12e5f5/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b0bd24ec7b0d770a64d28fe3fe12e5f5/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/8297f2dabb9719edd1659d08fb27cc10/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/8297f2dabb9719edd1659d08fb27cc10/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/39852eee3ae3c98d347fb7baf2c7615b/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/39852eee3ae3c98d347fb7baf2c7615b/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fc7aadd4f40d4faffc6421d977a5efdf/transformed/jetified-lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/fc7aadd4f40d4faffc6421d977a5efdf/transformed/jetified-lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/03cef59ae30cc738d9ad9e90e4e6a297/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/03cef59ae30cc738d9ad9e90e4e6a297/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/390511780a58212b9bcc53353a9a67f4/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/390511780a58212b9bcc53353a9a67f4/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/f4641ea73228121ffda497d7f917005e/transformed/jetified-lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/f4641ea73228121ffda497d7f917005e/transformed/jetified-lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/f944fd1f7ee44eb54123d84f12dcd93b/transformed/jetified-lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/f944fd1f7ee44eb54123d84f12dcd93b/transformed/jetified-lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/2d219c53b990020673e504ed09e916ad/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/2d219c53b990020673e504ed09e916ad/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/386436eb15f2a6ed3e0b42743092dc3a/transformed/jetified-core-ktx-1.9.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/386436eb15f2a6ed3e0b42743092dc3a/transformed/jetified-core-ktx-1.9.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.13/transforms/dcd58c4311b67921681d561f6c8e99f9/transformed/transition-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.13/transforms/dcd58c4311b67921681d561f6c8e99f9/transformed/transition-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/09a28d2de6989074da233c15dd999f2e/transformed/jetified-window-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/09a28d2de6989074da233c15dd999f2e/transformed/jetified-window-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/4d8a1eff85be8cf5617f063d344bac8a/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/4d8a1eff85be8cf5617f063d344bac8a/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/fb32ea96f6e2e5e5367b8c85544cc4d0/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/fb32ea96f6e2e5e5367b8c85544cc4d0/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/86b97bf4e36882146a24065f7a7b26b9/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/86b97bf4e36882146a24065f7a7b26b9/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/9fd03fa9ef81c27f51a7215de25f49d1/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/9fd03fa9ef81c27f51a7215de25f49d1/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/3c49456e0a380355fa746a924056ccf9/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/3c49456e0a380355fa746a924056ccf9/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/99b6a52dcee8cf4582415a4f71502ea0/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/99b6a52dcee8cf4582415a4f71502ea0/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/8466d4ef212ac1c6dc08c0b6f9141437/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/8466d4ef212ac1c6dc08c0b6f9141437/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/974558053e172ebbdc10993b111d52e8/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/974558053e172ebbdc10993b111d52e8/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/3110a38ff0bd5ea4f95a0f5b01160d70/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/3110a38ff0bd5ea4f95a0f5b01160d70/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/8ca61c5c50caec4fb1c9c9a3eeb803d2/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/8ca61c5c50caec4fb1c9c9a3eeb803d2/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/4dcb73d6529b6faa07265bbbd72c20da/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/4dcb73d6529b6faa07265bbbd72c20da/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/paker1/app/src/main/AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/7d4c1130c1a3f20467892e9db1658fa0/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/39852eee3ae3c98d347fb7baf2c7615b/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/39852eee3ae3c98d347fb7baf2c7615b/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/9fd03fa9ef81c27f51a7215de25f49d1/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/9fd03fa9ef81c27f51a7215de25f49d1/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/7d4c1130c1a3f20467892e9db1658fa0/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/7d4c1130c1a3f20467892e9db1658fa0/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/7d4c1130c1a3f20467892e9db1658fa0/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/7d4c1130c1a3f20467892e9db1658fa0/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/7d4c1130c1a3f20467892e9db1658fa0/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/7d4c1130c1a3f20467892e9db1658fa0/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/7d4c1130c1a3f20467892e9db1658fa0/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/39852eee3ae3c98d347fb7baf2c7615b/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/39852eee3ae3c98d347fb7baf2c7615b/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/39852eee3ae3c98d347fb7baf2c7615b/transformed/jetified-lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/09a28d2de6989074da233c15dd999f2e/transformed/jetified-window-1.0.0/AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/09a28d2de6989074da233c15dd999f2e/transformed/jetified-window-1.0.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/09a28d2de6989074da233c15dd999f2e/transformed/jetified-window-1.0.0/AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/09a28d2de6989074da233c15dd999f2e/transformed/jetified-window-1.0.0/AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/09a28d2de6989074da233c15dd999f2e/transformed/jetified-window-1.0.0/AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/09a28d2de6989074da233c15dd999f2e/transformed/jetified-window-1.0.0/AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/AndroidManifest.xml:23:9-81
permission#com.developer.faker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/AndroidManifest.xml:26:22-94
uses-permission#com.developer.faker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.13/transforms/5418d86f9bf51acc009e025a9c494d67/transformed/core-1.9.0/AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/314b744c70912ee3af5fa9545a3968a9/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
