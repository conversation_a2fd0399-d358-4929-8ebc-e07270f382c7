<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 默认屏幕边距，根据 Android 设计指南 -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="nav_header_vertical_spacing">8dp</dimen>
    <dimen name="nav_header_height">176dp</dimen>
    <dimen name="fab_margin">16dp</dimen>

    <!-- 应用程序特定尺寸 -->
    <dimen name="_1dp">1dp</dimen>
    <dimen name="_2dp">2dp</dimen>
    <dimen name="_4dp">4dp</dimen>
    <dimen name="_5dp">5dp</dimen>
    <dimen name="_8dp">8dp</dimen>
    <dimen name="_10dp">10dp</dimen>
    <dimen name="_12sp">12sp</dimen>
    <dimen name="_13sp">13sp</dimen>
    <dimen name="_14sp">14sp</dimen>
    <dimen name="_15dp">15dp</dimen>
    <dimen name="_16dp">16dp</dimen>
    <dimen name="_16sp">16sp</dimen>
    <dimen name="_18sp">18sp</dimen>
    <dimen name="_20dp">20dp</dimen>
    <dimen name="_20sp">20sp</dimen>
    <dimen name="_25dp">25dp</dimen>
    <dimen name="_30dp">30dp</dimen>
    <dimen name="_40dp">40dp</dimen>
    <dimen name="_50dp">50dp</dimen>
    <dimen name="_60dp">60dp</dimen>
    <dimen name="_70dp">70dp</dimen>
    <dimen name="_150dp">150dp</dimen>
    <dimen name="_280dp">280dp</dimen>
</resources>
