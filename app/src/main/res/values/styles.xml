<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 基础应用主题 -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <!-- 无操作栏主题 -->
    <style name="AppTheme.NoActionBar" parent="AppTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- AppBar 覆盖主题 -->
    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <!-- 弹出覆盖主题 -->
    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <!-- 登录编辑框样式 -->
    <style name="login_edit">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/login_edit</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textColorHint">@color/normal_text_color3</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
    </style>

    <!-- 工具栏样式 -->
    <style name="ToolbarColoredBackArrow" parent="AppTheme">
        <item name="android:textColorSecondary">@color/white</item>
    </style>

    <style name="toolbarWhiteText" parent="AppTheme">
        <item name="android:textColor">@color/white</item>
        <item name="android:textColorPrimary">@color/white</item>
    </style>
</resources>
